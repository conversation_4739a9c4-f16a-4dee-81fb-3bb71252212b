C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\MouvementStockRequest.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\ProductMarchandDto.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\ProductSourceDto.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\ProductMarchand.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\services\StockageService.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\StockManagementServiceApplication.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\ProductMarchandRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\ProduitDto.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\services\MapperService.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\services\StockJournalierService.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\StockDto.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\services\StockProduitService.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\controlleurs\StockageRestAdmin.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\StockInsufficientException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\StockJournalierRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\CreateProduitRequest.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\ProductSource.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\controlleurs\StockRestAdmin.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\StockProduit.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\StockException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\ProduitRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\DuplicateCodeException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\StockageNotFoundException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\StockProduitRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\controlleurs\StockProduitRestAdmin.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\Stock.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\InvalidOperationException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\controlleurs\StockJournalierRestAdmin.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\GlobalExceptionHandler.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\StockRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\ApiResponse.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\StockageDto.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\ProduitNotFoundException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\PageResponse.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\services\ProduitService.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\TypeProduit.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\StockageRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\StockSearchCriteria.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\StockJournalierDto.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\Stockage.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\StockJournalier.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\repositories\ProductSourceRepository.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\exceptions\StockNotFoundException.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\model\Produit.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\services\StockService.java
C:\Users\<USER>\Desktop\gantourocp\stockmanagementservice\src\main\java\com\example\stockmanagementservice\dtos\StockProduitDto.java
