<component name="libraryTable">
  <library name="Gradle: org.assertj:assertj-core:3.27.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.assertj" artifactId="assertj-core" version="3.27.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.assertj/assertj-core/3.27.3/31f5d58a202bd5df4993fb10fa2cffd610c20d6f/assertj-core-3.27.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.assertj/assertj-core/3.27.3/b22102e6c76782205e2bb8d5a9c900c<PERSON>efbcabb/assertj-core-3.27.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>