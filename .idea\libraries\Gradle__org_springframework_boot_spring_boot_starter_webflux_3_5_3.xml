<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-webflux:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-webflux" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-webflux/3.5.3/20ba20eaa7eb4921e1396242ec88ff41b9b85b27/spring-boot-starter-webflux-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-webflux/3.5.3/20ba20eaa7eb4921e1396242ec88ff41b9b85b27/spring-boot-starter-webflux-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>