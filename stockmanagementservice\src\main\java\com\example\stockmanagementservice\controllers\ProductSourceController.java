package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.ProductSourceDto;
import com.example.stockmanagementservice.model.ProductSource;
import com.example.stockmanagementservice.services.ProductSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des produits sources
 */
@RestController
@RequestMapping("/api/product-sources")
public class ProductSourceController {
    
    @Autowired
    private ProductSourceService productSourceService;
    
    // Créer un produit source
    @PostMapping
    public ResponseEntity<ProductSourceDto> createProductSource(@RequestBody ProductSourceDto productSourceDto) {
        try {
            ProductSource productSource = productSourceService.convertToEntity(productSourceDto);
            ProductSource savedProductSource = productSourceService.createProductSource(productSource);
            ProductSourceDto responseDto = productSourceService.convertToDto(savedProductSource);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer tous les produits sources
    @GetMapping
    public ResponseEntity<List<ProductSourceDto>> getAllProductSources() {
        try {
            List<ProductSource> productSources = productSourceService.getAllProductSources();
            List<ProductSourceDto> productSourcesDto = productSources.stream()
                .map(productSourceService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(productSourcesDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un produit source par ID
    @GetMapping("/{id}")
    public ResponseEntity<ProductSourceDto> getProductSourceById(@PathVariable Long id) {
        try {
            Optional<ProductSource> productSource = productSourceService.getProductSourceById(id);
            if (productSource.isPresent()) {
                ProductSourceDto productSourceDto = productSourceService.convertToDto(productSource.get());
                return new ResponseEntity<>(productSourceDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un produit source par code
    @GetMapping("/code/{code}")
    public ResponseEntity<ProductSourceDto> getProductSourceByCode(@PathVariable String code) {
        try {
            Optional<ProductSource> productSource = productSourceService.getProductSourceByCode(code);
            if (productSource.isPresent()) {
                ProductSourceDto productSourceDto = productSourceService.convertToDto(productSource.get());
                return new ResponseEntity<>(productSourceDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Rechercher par libellé
    @GetMapping("/search")
    public ResponseEntity<List<ProductSourceDto>> searchProductSources(@RequestParam String libelle) {
        try {
            List<ProductSource> productSources = productSourceService.searchByLibelle(libelle);
            List<ProductSourceDto> productSourcesDto = productSources.stream()
                .map(productSourceService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(productSourcesDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les produits sources ordonnés par libellé
    @GetMapping("/ordered")
    public ResponseEntity<List<ProductSourceDto>> getProductSourcesOrdered() {
        try {
            List<ProductSource> productSources = productSourceService.getProductSourcesOrderedByLibelle();
            List<ProductSourceDto> productSourcesDto = productSources.stream()
                .map(productSourceService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(productSourcesDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour un produit source
    @PutMapping("/{id}")
    public ResponseEntity<ProductSourceDto> updateProductSource(@PathVariable Long id, @RequestBody ProductSourceDto productSourceDto) {
        try {
            ProductSource productSourceDetails = productSourceService.convertToEntity(productSourceDto);
            ProductSource updatedProductSource = productSourceService.updateProductSource(id, productSourceDetails);
            if (updatedProductSource != null) {
                ProductSourceDto responseDto = productSourceService.convertToDto(updatedProductSource);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer un produit source
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteProductSource(@PathVariable Long id) {
        try {
            boolean deleted = productSourceService.deleteProductSource(id);
            if (deleted) {
                return new ResponseEntity<>("Produit source supprimé avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Produit source non trouvé", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
