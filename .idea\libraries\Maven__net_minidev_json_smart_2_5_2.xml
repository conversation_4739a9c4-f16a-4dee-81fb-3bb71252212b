<component name="libraryTable">
  <library name="Maven: net.minidev:json-smart:2.5.2" type="java-imported" external-system-id="Maven">
    <properties groupId="net.minidev" artifactId="json-smart" version="2.5.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/net/minidev/json-smart/2.5.2/json-smart-2.5.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/net/minidev/json-smart/2.5.2/json-smart-2.5.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>