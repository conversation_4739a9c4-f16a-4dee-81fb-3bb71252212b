<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.core:jersey-common:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.core" artifactId="jersey-common" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-common/3.1.10/d9d17fdec864f33de58efe240ec53816f95c03f3/jersey-common-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-common/3.1.10/9178641cb97e20d02f8f056ef0de5cf147bc4ad2/jersey-common-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>