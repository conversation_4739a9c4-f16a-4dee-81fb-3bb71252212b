<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-test-autoconfigure:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-test-autoconfigure" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/3.5.3/50d834241863042463113287b71bfc719d32dac1/spring-boot-test-autoconfigure-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/3.5.3/4a141b0a6b971688c0685ca52bcdc4bef97a10c0/spring-boot-test-autoconfigure-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>