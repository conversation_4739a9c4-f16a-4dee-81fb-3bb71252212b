<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-oauth2-client:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-oauth2-client" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-oauth2-client/3.5.3/eb627605ca4f49c7e775abd0904f5e2fa0c000d1/spring-boot-starter-oauth2-client-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-oauth2-client/3.5.3/eb627605ca4f49c7e775abd0904f5e2fa0c000d1/spring-boot-starter-oauth2-client-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>