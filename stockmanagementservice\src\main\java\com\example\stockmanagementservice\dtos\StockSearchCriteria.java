package com.example.stockmanagementservice.dtos;

import com.example.stockmanagementservice.model.TypeProduit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO pour les critères de recherche et filtrage des stocks
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockSearchCriteria {
    
    private String produitCode;
    private String produitLibelle;
    private TypeProduit typeProduit;
    
    private String stockCode;
    private String stockLibelle;
    
    private String stockageCode;
    private String stockageLibelle;
    
    private LocalDate dateDebut;
    private LocalDate dateFin;
    
    private BigDecimal quantiteMin;
    private BigDecimal quantiteMax;
    
    // Paramètres de pagination
    private int page = 0;
    private int size = 20;
    private String sortBy = "id";
    private String sortDirection = "ASC";
} 
