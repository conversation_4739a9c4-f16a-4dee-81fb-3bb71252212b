package com.example.stocmangementservice.exceptions;

/**
 * Exception levée quand un produit n'est pas trouvé
 */
public class ProduitNotFoundException extends StockException {
    
    public ProduitNotFoundException(Long id) {
        super("Produit avec l'ID " + id + " non trouvé");
    }
    
    public ProduitNotFoundException(String code) {
        super("Produit avec le code " + code + " non trouvé");
    }
    
    public ProduitNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
} 