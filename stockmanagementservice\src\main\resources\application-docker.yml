spring:
  application:
    name: STOCK-MANAGEMENT-SERVICE
  
  # Configuration PostgreSQL pour Docker
  datasource:
    url: ***********************************************************
    username: stock_user
    password: stock_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: true
        format_sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect

eureka:
  client:
    service-url:
      defaultZone: http://eureka-server:8761/eureka/
  instance:
    prefer-ip-address: true
    hostname: stock-management-service

server:
  port: 8081

logging:
  level:
    com.example: INFO
    org.springframework.cloud: DEBUG
