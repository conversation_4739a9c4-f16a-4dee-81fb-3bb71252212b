<component name="libraryTable">
  <library name="Gradle: jakarta.persistence:jakarta.persistence-api:3.1.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.persistence" artifactId="jakarta.persistence-api" version="3.1.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.persistence/jakarta.persistence-api/3.1.0/66901fa1c373c6aff65c13791cc11da72060a8d6/jakarta.persistence-api-3.1.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.persistence/jakarta.persistence-api/3.1.0/8d479b8d71750d5c2b8e58034b80231994cba6ec/jakarta.persistence-api-3.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>