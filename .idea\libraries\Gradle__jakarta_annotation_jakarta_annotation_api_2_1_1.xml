<component name="libraryTable">
  <library name="Gradle: jakarta.annotation:jakarta.annotation-api:2.1.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.annotation" artifactId="jakarta.annotation-api" version="2.1.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/2.1.1/48b9bda22b091b1f48b13af03fe36db3be6e1ae3/jakarta.annotation-api-2.1.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/2.1.1/3beea3ed2e687d9bd8a78c00e18951fffccefe90/jakarta.annotation-api-2.1.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>