<component name="libraryTable">
  <library name="Maven: jakarta.transaction:jakarta.transaction-api:2.0.1" type="java-imported" external-system-id="Maven">
    <properties groupId="jakarta.transaction" artifactId="jakarta.transaction-api" version="2.0.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>