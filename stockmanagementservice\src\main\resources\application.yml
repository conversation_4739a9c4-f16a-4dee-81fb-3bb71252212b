spring:
  application:
    name: STOCK-MANAGEMENT-SERVICE
  cloud:
    compatibility-verifier:
      enabled: false

  # Configuration par défaut (développement local avec H2)
  datasource:
    url: jdbc:h2:mem:stockdb
    username: sa
    password:
    driver-class-name: org.h2.Driver

  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/

server:
  port: 8081

# Configuration Swagger/OpenAPI
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
  info:
    title: "Stock Management Service OCP"
    description: "API de gestion des stocks pour OCP"
    version: "1.0.0"

---
# <PERSON>il <PERSON>er (PostgreSQL)
spring:
  config:
    activate:
      on-profile: docker

  datasource:
    url: ***********************************************************
    username: stock_user
    password: stock_password
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: true