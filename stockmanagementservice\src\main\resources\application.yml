spring:
  application:
    name: STOCK-MANAGEMENT-SERVICE
  cloud:
    compatibility-verifier:
      enabled: false

  # Configuration PostgreSQL
  datasource:
    url: *************************************************
    username: stock_user
    password: stock_password
    driver-class-name: org.postgresql.Driver

  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      defer-datasource-initialization: true

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/

server:
  port: 8081

# Configuration Swagger/OpenAPI
springdoc:
  swagger-ui:
    enabled: true

---
# Profil Docker (PostgreSQL)
spring:
  config:
    activate:
      on-profile: docker

  datasource:
    url: ***********************************************************
    username: stock_user
    password: stock_password
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: true