<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-netflix-eureka-server:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-netflix-eureka-server" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-netflix-eureka-server/4.3.0/a834d14d2dc5fb36c48c462f78b06b9506f3491c/spring-cloud-netflix-eureka-server-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-netflix-eureka-server/4.3.0/6cef0c5be313b3d99e1888024b8f15b772287ad8/spring-cloud-netflix-eureka-server-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>