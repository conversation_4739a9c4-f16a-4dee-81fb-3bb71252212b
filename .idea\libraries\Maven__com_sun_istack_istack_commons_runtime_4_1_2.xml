<component name="libraryTable">
  <library name="Maven: com.sun.istack:istack-commons-runtime:4.1.2" type="java-imported" external-system-id="Maven">
    <properties groupId="com.sun.istack" artifactId="istack-commons-runtime" version="4.1.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>