<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/3.5.3/b978e45d89b92eedaa09d0993bed91bcf00153d4/spring-boot-starter-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/3.5.3/d72bd5626d3121230b5bf93a19f0df41178a117a/spring-boot-starter-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>