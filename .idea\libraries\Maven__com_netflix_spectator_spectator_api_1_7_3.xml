<component name="libraryTable">
  <library name="Maven: com.netflix.spectator:spectator-api:1.7.3" type="java-imported" external-system-id="Maven">
    <properties groupId="com.netflix.spectator" artifactId="spectator-api" version="1.7.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/spectator/spectator-api/1.7.3/spectator-api-1.7.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/spectator/spectator-api/1.7.3/spectator-api-1.7.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/spectator/spectator-api/1.7.3/spectator-api-1.7.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>