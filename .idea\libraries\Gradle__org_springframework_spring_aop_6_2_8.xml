<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-aop:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-aop" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-aop/6.2.8/435fb310d5f464050b91ac3cb1a7f217a471e62e/spring-aop-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-aop/6.2.8/48c3196037b9b37e21a041e08b9bf6485ec8038f/spring-aop-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>