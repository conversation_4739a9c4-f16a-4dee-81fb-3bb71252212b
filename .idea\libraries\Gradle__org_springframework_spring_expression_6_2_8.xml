<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-expression:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-expression" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-expression/6.2.8/19e6618120fab6a572880675e1c0af676c7aa4ac/spring-expression-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-expression/6.2.8/87cf1f537b1d87ec1387a8f815a3060d0c220329/spring-expression-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>