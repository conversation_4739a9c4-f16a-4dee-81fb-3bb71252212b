<component name="libraryTable">
  <library name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.24.3" type="java-imported" external-system-id="Maven">
    <properties groupId="org.apache.logging.log4j" artifactId="log4j-to-slf4j" version="2.24.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>