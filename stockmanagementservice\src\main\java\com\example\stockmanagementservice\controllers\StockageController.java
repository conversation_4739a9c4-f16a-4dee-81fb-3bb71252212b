package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.StockageDto;
import com.example.stockmanagementservice.model.Stockage;
import com.example.stockmanagementservice.services.StockageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des stockages
 */
@RestController
@RequestMapping("/api/stockages")
public class StockageController {
    
    @Autowired
    private StockageService stockageService;
    
    // Créer un stockage
    @PostMapping
    public ResponseEntity<StockageDto> createStockage(@RequestBody StockageDto stockageDto) {
        try {
            Stockage stockage = stockageService.convertToEntity(stockageDto);
            Stockage savedStockage = stockageService.createStockage(stockage);
            StockageDto responseDto = stockageService.convertToDto(savedStockage);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer tous les stockages
    @GetMapping
    public ResponseEntity<List<StockageDto>> getAllStockages() {
        try {
            List<Stockage> stockages = stockageService.getAllStockages();
            List<StockageDto> stockagesDto = stockages.stream()
                .map(stockageService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockagesDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un stockage par ID
    @GetMapping("/{id}")
    public ResponseEntity<StockageDto> getStockageById(@PathVariable Long id) {
        try {
            Optional<Stockage> stockage = stockageService.getStockageById(id);
            if (stockage.isPresent()) {
                StockageDto stockageDto = stockageService.convertToDto(stockage.get());
                return new ResponseEntity<>(stockageDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un stockage par code
    @GetMapping("/code/{code}")
    public ResponseEntity<StockageDto> getStockageByCode(@PathVariable String code) {
        try {
            Optional<Stockage> stockage = stockageService.getStockageByCode(code);
            if (stockage.isPresent()) {
                StockageDto stockageDto = stockageService.convertToDto(stockage.get());
                return new ResponseEntity<>(stockageDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Rechercher par libellé
    @GetMapping("/search")
    public ResponseEntity<List<StockageDto>> searchStockages(@RequestParam String libelle) {
        try {
            List<Stockage> stockages = stockageService.searchByLibelle(libelle);
            List<StockageDto> stockagesDto = stockages.stream()
                .map(stockageService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockagesDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stockages ordonnés par libellé
    @GetMapping("/ordered")
    public ResponseEntity<List<StockageDto>> getStockagesOrdered() {
        try {
            List<Stockage> stockages = stockageService.getStockagesOrderedByLibelle();
            List<StockageDto> stockagesDto = stockages.stream()
                .map(stockageService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockagesDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour un stockage
    @PutMapping("/{id}")
    public ResponseEntity<StockageDto> updateStockage(@PathVariable Long id, @RequestBody StockageDto stockageDto) {
        try {
            Stockage stockageDetails = stockageService.convertToEntity(stockageDto);
            Stockage updatedStockage = stockageService.updateStockage(id, stockageDetails);
            if (updatedStockage != null) {
                StockageDto responseDto = stockageService.convertToDto(updatedStockage);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer un stockage
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteStockage(@PathVariable Long id) {
        try {
            boolean deleted = stockageService.deleteStockage(id);
            if (deleted) {
                return new ResponseEntity<>("Stockage supprimé avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Stockage non trouvé", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
