package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.StockProduitDto;
import com.example.stockmanagementservice.model.StockProduit;
import com.example.stockmanagementservice.repositories.StockProduitRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des relations stock-produit - CRUD de base
 */
@Service
public class StockProduitService {
    
    @Autowired
    private StockProduitRepository stockProduitRepository;
    
    // Créer une relation stock-produit
    public StockProduit createStockProduit(StockProduit stockProduit) {
        return stockProduitRepository.save(stockProduit);
    }
    
    // Récupérer toutes les relations stock-produit
    public List<StockProduit> getAllStockProduits() {
        return stockProduitRepository.findAll();
    }
    
    // Récupérer une relation par ID
    public Optional<StockProduit> getStockProduitById(Long id) {
        return stockProduitRepository.findById(id);
    }
    
    // Récupérer les produits d'un stock
    public List<StockProduit> getStockProduitsByStockId(Long stockId) {
        return stockProduitRepository.findByStockId(stockId);
    }
    
    // Récupérer les stocks d'un produit
    public List<StockProduit> getStockProduitsByProduitId(Long produitId) {
        return stockProduitRepository.findByProduitId(produitId);
    }
    
    // Récupérer une relation spécifique stock-produit
    public List<StockProduit> getStockProduitsByStockAndProduit(Long stockId, Long produitId) {
        return stockProduitRepository.findByStockIdAndProduitId(stockId, produitId);
    }
    
    // Mettre à jour une relation stock-produit
    public StockProduit updateStockProduit(Long id, StockProduit stockProduitDetails) {
        Optional<StockProduit> optionalStockProduit = stockProduitRepository.findById(id);
        if (optionalStockProduit.isPresent()) {
            StockProduit stockProduit = optionalStockProduit.get();
            stockProduit.setQuantite(stockProduitDetails.getQuantite());
            return stockProduitRepository.save(stockProduit);
        }
        return null;
    }
    
    // Supprimer une relation stock-produit
    public boolean deleteStockProduit(Long id) {
        if (stockProduitRepository.existsById(id)) {
            stockProduitRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Convertir entité vers DTO
    public StockProduitDto convertToDto(StockProduit stockProduit) {
        return new StockProduitDto(
            stockProduit.getId(),
            stockProduit.getStock().getId(),
            stockProduit.getProduit().getId(),
            stockProduit.getQuantite()
        );
    }
    
    // Convertir DTO vers entité (nécessite les entités Stock et Produit)
    public StockProduit convertToEntity(StockProduitDto dto) {
        StockProduit stockProduit = new StockProduit();
        stockProduit.setId(dto.getId());
        stockProduit.setQuantite(dto.getQuantite());
        // Note: Les entités Stock et Produit doivent être définies séparément
        return stockProduit;
    }
}
