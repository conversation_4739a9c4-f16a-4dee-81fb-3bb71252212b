package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.StockProduitDto;
import com.example.stockmanagementservice.exceptions.DuplicateCodeException;
import com.example.stockmanagementservice.exceptions.InvalidOperationException;
import com.example.stockmanagementservice.exceptions.StockNotFoundException;
import com.example.stockmanagementservice.model.Produit;
import com.example.stockmanagementservice.model.Stock;
import com.example.stockmanagementservice.model.StockJournalier;
import com.example.stockmanagementservice.model.StockProduit;
import com.example.stockmanagementservice.repositories.StockProduitRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des relations Stock-Produit
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class StockProduitService {
    
    private final StockProduitRepository stockProduitRepository;
    private final StockService stockService;
    private final ProduitService produitService;
    
    /**
     * Créer une nouvelle relation stock-produit
     */
    public StockProduit createStockProduit(StockProduitDto stockProduitDto) {
        log.info("Création d'une relation stock-produit : Stock ID={}, Produit ID={}", 
                stockProduitDto.getStockId(), stockProduitDto.getProduitId());
        
        // Vérifier que la relation n'existe pas déjà
        if (stockProduitRepository.existsByStockIdAndProduitId(
                stockProduitDto.getStockId(), stockProduitDto.getProduitId())) {
            throw new DuplicateCodeException("Relation Stock-Produit", 
                    "Stock " + stockProduitDto.getStockId() + " - Produit " + stockProduitDto.getProduitId());
        }
        
        // Récupérer les entités
        Stock stock = stockService.findById(stockProduitDto.getStockId());
        Produit produit = produitService.findById(stockProduitDto.getProduitId());
        
        StockProduit stockProduit = new StockProduit();
        stockProduit.setStock(stock);
        stockProduit.setProduit(produit);
        
        stockProduit = stockProduitRepository.save(stockProduit);
        log.info("Relation stock-produit créée avec succès : ID={}", stockProduit.getId());
        
        return stockProduit;
    }
    
    /**
     * Rechercher une relation par ID
     */
    @Transactional(readOnly = true)
    public StockProduit findById(Long id) {
        return stockProduitRepository.findById(id)
                .orElseThrow(() -> new StockNotFoundException(id));
    }
    
    /**
     * Rechercher par stock et produit
     */
    @Transactional(readOnly = true)
    public Optional<StockProduit> findByStockAndProduit(Stock stock, Produit produit) {
        return stockProduitRepository.findByStockAndProduit(stock, produit);
    }
    
    /**
     * Rechercher par IDs de stock et produit
     */
    @Transactional(readOnly = true)
    public Optional<StockProduit> findByStockIdAndProduitId(Long stockId, Long produitId) {
        return stockProduitRepository.findByStockIdAndProduitId(stockId, produitId);
    }
    
    /**
     * Rechercher toutes les relations avec pagination
     */
    @Transactional(readOnly = true)
    public Page<StockProduit> findAll(Pageable pageable) {
        return stockProduitRepository.findAll(pageable);
    }
    
    /**
     * Rechercher les relations par stock
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByStock(Stock stock) {
        return stockProduitRepository.findByStock(stock);
    }
    
    /**
     * Rechercher les relations par ID de stock
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByStockId(Long stockId) {
        return stockProduitRepository.findByStockId(stockId);
    }
    
    /**
     * Rechercher les relations par produit
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByProduit(Produit produit) {
        return stockProduitRepository.findByProduit(produit);
    }
    
    /**
     * Rechercher les relations par ID de produit
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByProduitId(Long produitId) {
        return stockProduitRepository.findByProduitId(produitId);
    }
    
    /**
     * Rechercher par code de stock
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByStockCode(String stockCode) {
        return stockProduitRepository.findByStockCode(stockCode);
    }
    
    /**
     * Rechercher par code de produit
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByProduitCode(String produitCode) {
        return stockProduitRepository.findByProduitCode(produitCode);
    }
    
    /**
     * Rechercher par stockage (via stock)
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findByStockageId(Long stockageId) {
        return stockProduitRepository.findByStockageId(stockageId);
    }
    
    /**
     * Recherche multicritères avec pagination
     */
    @Transactional(readOnly = true)
    public Page<StockProduit> searchStockProduits(Long stockId, Long produitId, 
                                                  String stockCode, String produitCode, 
                                                  Pageable pageable) {
        return stockProduitRepository.findBySearchCriteria(stockId, produitId, stockCode, produitCode, pageable);
    }
    
    /**
     * Supprimer une relation stock-produit
     */
    public void deleteStockProduit(Long id) {
        log.info("Suppression de la relation stock-produit ID={}", id);
        
        StockProduit stockProduit = findById(id);
        
        // Vérifier s'il n'y a pas de suivis journaliers
        if (!stockProduit.getStockJournaliers().isEmpty()) {
            throw new InvalidOperationException("Suppression", 
                    "Impossible de supprimer une relation ayant des suivis journaliers");
        }
        
        stockProduitRepository.deleteById(id);
        log.info("Relation stock-produit supprimée avec succès : ID={}", id);
    }
    
    /**
     * Supprimer par IDs de stock et produit
     */
    public void deleteByStockIdAndProduitId(Long stockId, Long produitId) {
        Optional<StockProduit> stockProduit = findByStockIdAndProduitId(stockId, produitId);
        if (stockProduit.isPresent()) {
            deleteStockProduit(stockProduit.get().getId());
        } else {
            throw new StockNotFoundException("Relation stock-produit non trouvée pour Stock ID=" + 
                    stockId + " et Produit ID=" + produitId);
        }
    }
    
    /**
     * Rechercher les relations ayant des suivis journaliers
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findStockProduitsWithJournaliers() {
        return stockProduitRepository.findStockProduitsWithJournaliers();
    }
    
    /**
     * Rechercher les relations sans suivis journaliers
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findStockProduitsSansSuivi() {
        return stockProduitRepository.findStockProduitsSansSuivi();
    }
    
    /**
     * Compter les suivis journaliers par relation
     */
    @Transactional(readOnly = true)
    public List<Object[]> countJournaliersByStockProduit() {
        return stockProduitRepository.countJournaliersByStockProduit();
    }
    
    /**
     * Récupérer les suivis journaliers d'une relation
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> getStockJournaliersOfStockProduit(Long stockProduitId) {
        StockProduit stockProduit = findById(stockProduitId);
        return stockProduit.getStockJournaliers();
    }
    
    /**
     * Vérifier l'existence d'une relation
     */
    @Transactional(readOnly = true)
    public boolean existsByStockIdAndProduitId(Long stockId, Long produitId) {
        return stockProduitRepository.existsByStockIdAndProduitId(stockId, produitId);
    }
    
    /**
     * Compter le nombre total de relations
     */
    @Transactional(readOnly = true)
    public long count() {
        return stockProduitRepository.count();
    }
    
    /**
     * Rechercher toutes les relations (sans pagination)
     */
    @Transactional(readOnly = true)
    public List<StockProduit> findAll() {
        return stockProduitRepository.findAll();
    }
    
    /**
     * Associer un produit à un stock (méthode utilitaire)
     */
    public StockProduit associerProduitAuStock(Long stockId, Long produitId) {
        log.info("Association du produit ID={} au stock ID={}", produitId, stockId);
        
        StockProduitDto dto = new StockProduitDto();
        dto.setStockId(stockId);
        dto.setProduitId(produitId);
        
        return createStockProduit(dto);
    }
    
    /**
     * Dissocier un produit d'un stock (méthode utilitaire)
     */
    public void dissocierProduitDuStock(Long stockId, Long produitId) {
        log.info("Dissociation du produit ID={} du stock ID={}", produitId, stockId);
        deleteByStockIdAndProduitId(stockId, produitId);
    }
} 
