<component name="libraryTable">
  <library name="Gradle: org.hibernate.validator:hibernate-validator:8.0.2.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.hibernate.validator" artifactId="hibernate-validator" version="8.0.2.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hibernate.validator/hibernate-validator/8.0.2.Final/220e64815dd87535525331de20570017f899eb13/hibernate-validator-8.0.2.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hibernate.validator/hibernate-validator/8.0.2.Final/1fd7ccd2a5bd61303f58e56aa3de6a388247951f/hibernate-validator-8.0.2.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>