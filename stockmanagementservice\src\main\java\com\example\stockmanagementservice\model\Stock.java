package com.example.stockmanagementservice.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Entity
@Table(name = "stock")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Stock {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String libelle;
    
    @Column(nullable = false, unique = true)
    private String code;
    @OneToMany(mappedBy ="stock",cascade = CascadeType.ALL,fetch=FetchType.LAZY)
    private  List<StockJournalier> stockJournaliers;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stockage_id", nullable = false)
    private Stockage stockage;
    
    @OneToMany(mappedBy = "stock", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StockProduit> stockProduits;
} 
