<component name="libraryTable">
  <library name="Maven: com.h2database:h2:2.3.232" type="java-imported" external-system-id="Maven">
    <properties groupId="com.h2database" artifactId="h2" version="2.3.232" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/h2database/h2/2.3.232/h2-2.3.232.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/h2database/h2/2.3.232/h2-2.3.232-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/h2database/h2/2.3.232/h2-2.3.232-sources.jar!/" />
    </SOURCES>
  </library>
</component>