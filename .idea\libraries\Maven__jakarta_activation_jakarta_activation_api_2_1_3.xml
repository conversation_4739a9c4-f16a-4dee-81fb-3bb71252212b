<component name="libraryTable">
  <library name="Maven: jakarta.activation:jakarta.activation-api:2.1.3" type="java-imported" external-system-id="Maven">
    <properties groupId="jakarta.activation" artifactId="jakarta.activation-api" version="2.1.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>