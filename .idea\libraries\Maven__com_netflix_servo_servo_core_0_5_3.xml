<component name="libraryTable">
  <library name="Maven: com.netflix.servo:servo-core:0.5.3" type="java-imported" external-system-id="Maven">
    <properties groupId="com.netflix.servo" artifactId="servo-core" version="0.5.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/servo/servo-core/0.5.3/servo-core-0.5.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/servo/servo-core/0.5.3/servo-core-0.5.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/servo/servo-core/0.5.3/servo-core-0.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>