<component name="libraryTable">
  <library name="Gradle: net.minidev:accessors-smart:2.5.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="net.minidev" artifactId="accessors-smart" version="2.5.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/accessors-smart/2.5.2/ce16fd235cfee48e67eda33e684423bba09f7d07/accessors-smart-2.5.2.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/net.minidev/accessors-smart/2.5.2/a15e4228860d5dbb6b7c18070ee6cc798b5be48a/accessors-smart-2.5.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>