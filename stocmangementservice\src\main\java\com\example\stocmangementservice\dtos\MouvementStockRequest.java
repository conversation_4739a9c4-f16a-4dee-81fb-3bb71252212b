package com.example.stocmangementservice.dtos;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO pour les demandes de mouvement de stock
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MouvementStockRequest {
    
    @NotNull(message = "L'ID du stock-produit est obligatoire")
    private Long stockProduitId;
    
    @NotNull(message = "La quantité est obligatoire")
    @Positive(message = "La quantité doit être positive")
    private BigDecimal quantite;
    
    @NotNull(message = "La date est obligatoire")
    private LocalDate date;
    
    @NotNull(message = "Le type d'opération est obligatoire")
    private TypeOperation typeOperation;
    
    private String commentaire;
    
    public enum TypeOperation {
        ENTREE,
        SORTIE,
        AJUSTEMENT
    }
} 