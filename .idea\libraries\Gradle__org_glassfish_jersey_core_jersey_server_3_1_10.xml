<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.core:jersey-server:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.core" artifactId="jersey-server" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-server/3.1.10/c0c631cfa916560b24aa3f0af1550aec6a87e69f/jersey-server-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-server/3.1.10/9d884a75fcde4714605233d3e6279ee0b2df11a8/jersey-server-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>