<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-oauth2-jose:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-oauth2-jose" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-jose/6.5.1/7886128a22d12a824e4cdb340d3a22ac8cc6e8a6/spring-security-oauth2-jose-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-jose/6.5.1/5d274883960180a3ba65a05f5d264aca466cd294/spring-security-oauth2-jose-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>