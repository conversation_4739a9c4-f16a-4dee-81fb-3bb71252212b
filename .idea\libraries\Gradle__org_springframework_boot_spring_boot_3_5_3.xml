<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot/3.5.3/1b02976773afc2489df3c22000acaf83d0ada2b2/spring-boot-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot/3.5.3/5412d2799a00ed573d97dea99780fbf520c55c53/spring-boot-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>