<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-oauth2-client:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-oauth2-client" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-client/6.5.1/2984d0161405ed5aa355a2ced69ca458a15ab21c/spring-security-oauth2-client-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-client/6.5.1/f0fef70b8bccbc2504956d7d7efee4107a2da49f/spring-security-oauth2-client-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>