<component name="libraryTable">
  <library name="Gradle: io.micrometer:micrometer-commons:1.15.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.micrometer" artifactId="micrometer-commons" version="1.15.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-commons/1.15.1/43cbe2327f4e3f5cbfda6be64418c66b9b62f4ec/micrometer-commons-1.15.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-commons/1.15.1/549d682341c608cfbacde4f277d0a4eff7502df6/micrometer-commons-1.15.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>