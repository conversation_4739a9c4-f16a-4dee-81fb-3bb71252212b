<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-jcl:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-jcl" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jcl/6.2.8/b806d8d270a8eebaca56f6b4dae8f627460b0d4e/spring-jcl-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-jcl/6.2.8/83d824c6f97a26fe5d4cb3b1aae0a0d3021441f6/spring-jcl-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>