<component name="libraryTable">
  <library name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.1" type="java-imported" external-system-id="Maven">
    <properties groupId="com.fasterxml.jackson.module" artifactId="jackson-module-parameter-names" version="2.19.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>