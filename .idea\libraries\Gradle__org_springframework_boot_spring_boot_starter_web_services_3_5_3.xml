<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-web-services:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-web-services" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web-services/3.5.3/e553985b65ca3189c11eeef06925c211ad61eb75/spring-boot-starter-web-services-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web-services/3.5.3/e553985b65ca3189c11eeef06925c211ad61eb75/spring-boot-starter-web-services-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>