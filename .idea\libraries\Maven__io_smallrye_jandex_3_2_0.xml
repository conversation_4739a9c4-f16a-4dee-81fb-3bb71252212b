<component name="libraryTable">
  <library name="Maven: io.smallrye:jandex:3.2.0" type="java-imported" external-system-id="Maven">
    <properties groupId="io.smallrye" artifactId="jandex" version="3.2.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/smallrye/jandex/3.2.0/jandex-3.2.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/smallrye/jandex/3.2.0/jandex-3.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>