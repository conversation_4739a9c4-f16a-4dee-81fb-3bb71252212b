<component name="libraryTable">
  <library name="Maven: jakarta.validation:jakarta.validation-api:3.0.2" type="java-imported" external-system-id="Maven">
    <properties groupId="jakarta.validation" artifactId="jakarta.validation-api" version="3.0.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>