<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-web:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-web" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web/3.5.3/4ba7817f958e9a0b0f642af98440affd0c7e2ee0/spring-boot-starter-web-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-web/3.5.3/a226533b8ffeb76b0d580e026b596e9fc5f7cec9/spring-boot-starter-web-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>