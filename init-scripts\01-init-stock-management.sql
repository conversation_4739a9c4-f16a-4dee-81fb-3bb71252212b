-- Initialize Stock Management Database
-- This script runs automatically when PostgreSQL container starts

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS inventory;
-- CREATE SCHEMA IF NOT EXISTS reporting;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Example: Create initial tables (if not using JPA auto-creation)
-- CREATE TABLE IF NOT EXISTS products (
--     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
--     name VARCHAR(255) NOT NULL,
--     description TEXT,
--     price DECIMAL(10,2),
--     quantity INTEGER DEFAULT 0,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- Insert initial data if needed
-- INSERT INTO products (name, description, price, quantity) VALUES
-- ('Sample Product', 'This is a sample product', 99.99, 100)
-- ON CONFLICT DO NOTHING;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE stock_management TO stock_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO stock_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO stock_user;
