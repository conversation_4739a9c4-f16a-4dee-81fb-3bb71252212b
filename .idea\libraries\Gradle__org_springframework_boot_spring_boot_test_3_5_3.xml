<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-test:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-test" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/3.5.3/dd4c87534ae57510b01575c374eb8d55dd4efb92/spring-boot-test-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/3.5.3/c796e1b2a40e0b2538f973a36fd5fd3f9c1d5f7e/spring-boot-test-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>