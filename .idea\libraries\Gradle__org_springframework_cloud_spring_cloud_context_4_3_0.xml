<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-context:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-context" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-context/4.3.0/831b237375831042b7838570db74df44bdea5aa6/spring-cloud-context-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-context/4.3.0/8d418dd7af9a580e749eeef69e350e91db0ab5f6/spring-cloud-context-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>