package com.example.stocmangementservice.repositories;

import com.example.stocmangementservice.model.ProductMarchand;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository spécialisé pour ProductMarchand
 * Hérite automatiquement des méthodes de ProduitRepository via l'héritage JPA
 */
@Repository
public interface ProductMarchandRepository extends JpaRepository<ProductMarchand, Long> {
    
    /**
     * Recherche par code spécifique aux produits marchands
     */
    Optional<ProductMarchand> findByCode(String code);
    
    /**
     * Recherche par libellé pour les produits marchands
     */
    List<ProductMarchand> findByLibelleContainingIgnoreCase(String libelle);
    
    /**
     * Produits marchands exportables
     */
    List<ProductMarchand> findByExportTrue();
    
    /**
     * Produits marchands avec réclamation par défaut
     */
    List<ProductMarchand> findByDefaultReclamationTrue();
    
    /**
     * Recherche par style pour les produits marchands
     */
    List<ProductMarchand> findByStyleContainingIgnoreCase(String style);
    
    /**
     * Compter tous les produits marchands
     */
    @Query("SELECT COUNT(pm) FROM ProductMarchand pm")
    Long countAllProductMarchand();
    
    /**
     * Produits marchands les plus récents
     */
    @Query("SELECT pm FROM ProductMarchand pm ORDER BY pm.id DESC")
    List<ProductMarchand> findLatestProductMarchand();
} 