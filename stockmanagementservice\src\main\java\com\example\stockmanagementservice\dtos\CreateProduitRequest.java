package com.example.stockmanagementservice.dtos;

import com.example.stockmanagementservice.model.TypeProduit;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO pour les requêtes de création de produit
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateProduitRequest {
    
    @NotNull(message = "Le type de produit est obligatoire")
    private TypeProduit typeProduit;
    
    @NotBlank(message = "Le code est obligatoire")
    @Size(max = 255, message = "Le code ne peut pas dépasser 255 caractères")
    private String code;
    
    @NotBlank(message = "Le libellé est obligatoire")
    @Size(max = 255, message = "Le libellé ne peut pas dépasser 255 caractères")
    private String libelle;
    
    @Size(max = 500, message = "La description ne peut pas dépasser 500 caractères")
    private String description;
    
    @Size(max = 255, message = "Le style ne peut pas dépasser 255 caractères")
    private String style;
    
    private Boolean defaultReclamation = false;
    
    private Boolean export = false;
} 
