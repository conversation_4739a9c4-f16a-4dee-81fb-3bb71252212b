<component name="libraryTable">
  <library name="Gradle: io.netty:netty-resolver:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-resolver" version="4.1.122.Final" />
    <ANNOTATIONS>
      <root url="jar://$MAVEN_REPOSITORY$/org/jetbrains/externalAnnotations/io/netty/netty-resolver/4.1.27.Final-an1/netty-resolver-4.1.27.Final-an1-annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.122.Final/ee7672587266524b2256c0fcd1758da7c8998119/netty-resolver-4.1.122.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.122.Final/288ede8b739f73792d34333535c12a825638ed7a/netty-resolver-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>