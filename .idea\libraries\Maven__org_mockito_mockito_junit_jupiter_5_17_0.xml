<component name="libraryTable">
  <library name="Maven: org.mockito:mockito-junit-jupiter:5.17.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.mockito" artifactId="mockito-junit-jupiter" version="5.17.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>