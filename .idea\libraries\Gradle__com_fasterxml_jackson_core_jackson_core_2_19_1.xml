<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.core:jackson-core:2.19.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-core" version="2.19.1" />
    <ANNOTATIONS>
      <root url="jar://$MAVEN_REPOSITORY$/org/jetbrains/externalAnnotations/com/fasterxml/jackson/core/jackson-core/2.9.6-an1/jackson-core-2.9.6-an1-annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.19.1/6e5a8cb8a6cada322497cefb7726657d98aaee15/jackson-core-2.19.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.19.1/7c08eb9308a088957cb1fc9ff93f5bc10c2bcf27/jackson-core-2.19.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>