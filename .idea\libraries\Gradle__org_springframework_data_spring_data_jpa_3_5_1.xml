<component name="libraryTable">
  <library name="Gradle: org.springframework.data:spring-data-jpa:3.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.data" artifactId="spring-data-jpa" version="3.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-jpa/3.5.1/7d34c2d8c8bcd96f5ce3f012c2a88d682a86e14b/spring-data-jpa-3.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-jpa/3.5.1/acf3dc26fbbd0b91022cb937fdccbe61f4d0aed2/spring-data-jpa-3.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>