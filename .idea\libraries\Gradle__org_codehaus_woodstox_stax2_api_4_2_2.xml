<component name="libraryTable">
  <library name="Gradle: org.codehaus.woodstox:stax2-api:4.2.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.codehaus.woodstox" artifactId="stax2-api" version="4.2.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.woodstox/stax2-api/4.2.2/b0d746cadea928e5264f2ea294ea9a1bf815bbde/stax2-api-4.2.2.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.codehaus.woodstox/stax2-api/4.2.2/89bd0d99524eebd106cd1941bfb7469af95511f0/stax2-api-4.2.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>