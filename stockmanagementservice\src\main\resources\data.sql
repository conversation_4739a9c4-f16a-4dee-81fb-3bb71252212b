-- Données initiales pour OCP Stock Management
-- Ce fichier est exécuté automatiquement par Spring Boot après la création des tables

-- Insertion des STOCKAGES OCP
INSERT INTO stockage (code, libelle) VALUES
('BEN_GUERIR', 'Stockage Ben Guerir'),
('MZINDA', 'Stockage Mzinda'),
('JORF_SFER', 'Stockage Jorf Sfer');

-- Insertion des STOCKS (Niveaux Haut/Bas)
INSERT INTO stocks (code, libelle, quantite_actuelle, quantite_min, quantite_max, created_at) VALUES
('STOCK_HAUT', 'Stock Niveau Haut', 1000.0, 100.0, 2000.0, CURRENT_TIMESTAMP),
('STOCK_BAS', 'Stock Niveau Bas', 500.0, 50.0, 1000.0, CURRENT_TIMESTAMP)
ON CONFLICT (code) DO NOTHING;

-- Insertion des PRODUITS MARCHANDS OCP (Produits finis pour la vente)
INSERT INTO produits (code, libelle, type_produit, prix_unitaire, created_at) VALUES
('PHOS_DAP', 'Phosphate DAP (Diammonium Phosphate)', 'MARCHAND', 450.00, CURRENT_TIMESTAMP),
('PHOS_TSP', 'Phosphate TSP (Triple Super Phosphate)', 'MARCHAND', 380.00, CURRENT_TIMESTAMP),
('PHOS_MAP', 'Phosphate MAP (Monoammonium Phosphate)', 'MARCHAND', 420.00, CURRENT_TIMESTAMP),
('ACIDE_PHOS', 'Acide Phosphorique P2O5', 'MARCHAND', 650.00, CURRENT_TIMESTAMP),
('ENGRAIS_NPK', 'Engrais NPK Complexe', 'MARCHAND', 320.00, CURRENT_TIMESTAMP),
('PHOS_ROCHE_CONC', 'Phosphate de Roche Concentré', 'MARCHAND', 280.00, CURRENT_TIMESTAMP)
ON CONFLICT (code) DO NOTHING;

-- Insertion des PRODUITS SOURCES OCP (Matières premières)
INSERT INTO produits (code, libelle, type_produit, prix_unitaire, created_at) VALUES
('ROCHE_PHOS_BRUTE', 'Roche Phosphatée Brute', 'SOURCE', 180.00, CURRENT_TIMESTAMP),
('SOUFRE_BRUT', 'Soufre Brut Importé', 'SOURCE', 220.00, CURRENT_TIMESTAMP),
('AMMONIAC_LIQ', 'Ammoniac Liquide', 'SOURCE', 380.00, CURRENT_TIMESTAMP),
('ACIDE_SULF', 'Acide Sulfurique Concentré', 'SOURCE', 150.00, CURRENT_TIMESTAMP),
('GAZ_NATUREL', 'Gaz Naturel pour Production', 'SOURCE', 120.00, CURRENT_TIMESTAMP),
('MINERAI_PHOS', 'Minerai de Phosphate Khouribga', 'SOURCE', 200.00, CURRENT_TIMESTAMP)
ON CONFLICT (code) DO NOTHING;

-- Insertion des PRODUCT_MARCHANDS (Détails des produits marchands)
INSERT INTO product_marchands (code, libelle, description, prix_vente, marche_cible, created_at) VALUES
('PM_DAP', 'DAP Export', 'Diammonium Phosphate pour export international', 480.00, 'International', CURRENT_TIMESTAMP),
('PM_TSP', 'TSP Local', 'Triple Super Phosphate pour marché local', 400.00, 'National', CURRENT_TIMESTAMP),
('PM_MAP', 'MAP Premium', 'Monoammonium Phosphate qualité premium', 450.00, 'International', CURRENT_TIMESTAMP),
('PM_ACIDE', 'Acide Phosphorique Industriel', 'Acide phosphorique pour industrie', 680.00, 'Industriel', CURRENT_TIMESTAMP)
ON CONFLICT (code) DO NOTHING;

-- Insertion des PRODUCT_SOURCES (Détails des produits sources)
INSERT INTO product_sources (code, libelle, description, fournisseur, pays_origine, created_at) VALUES
('PS_ROCHE', 'Roche Phosphatée Khouribga', 'Extraction mine de Khouribga', 'OCP Mining', 'Maroc', CURRENT_TIMESTAMP),
('PS_SOUFRE', 'Soufre Raffiné', 'Soufre importé pour production acide', 'Fournisseur International', 'Canada', CURRENT_TIMESTAMP),
('PS_AMMONIAC', 'Ammoniac Technique', 'Ammoniac pour production engrais', 'Fournisseur Chimique', 'Russie', CURRENT_TIMESTAMP),
('PS_GAZ', 'Gaz Naturel Pipeline', 'Gaz naturel pour énergie', 'ONEE', 'Maroc', CURRENT_TIMESTAMP)
ON CONFLICT (code) DO NOTHING;

-- Insertion des relations STOCK_PRODUITS (Associer stocks et produits)
INSERT INTO stock_produits (stock_id, produit_id, quantite, created_at) VALUES
-- Stock Haut avec produits marchands
(1, 1, 200.0, CURRENT_TIMESTAMP), -- STOCK_HAUT + PHOS_DAP
(1, 2, 150.0, CURRENT_TIMESTAMP), -- STOCK_HAUT + PHOS_TSP
(1, 3, 180.0, CURRENT_TIMESTAMP), -- STOCK_HAUT + PHOS_MAP
-- Stock Bas avec produits sources
(2, 7, 300.0, CURRENT_TIMESTAMP), -- STOCK_BAS + ROCHE_PHOS_BRUTE
(2, 8, 100.0, CURRENT_TIMESTAMP), -- STOCK_BAS + SOUFRE_BRUT
(2, 9, 80.0, CURRENT_TIMESTAMP)   -- STOCK_BAS + AMMONIAC_LIQ
ON CONFLICT DO NOTHING;

-- Insertion des STOCKS_JOURNALIERS (Historique des mouvements)
INSERT INTO stock_journaliers (stock_id, jour, quantite_debut, quantite_entree, quantite_sortie, quantite_fin, created_at) VALUES
-- Mouvements pour Stock Haut
(1, CURRENT_DATE - INTERVAL '2 days', 800.0, 300.0, 100.0, 1000.0, CURRENT_TIMESTAMP - INTERVAL '2 days'),
(1, CURRENT_DATE - INTERVAL '1 day', 1000.0, 200.0, 150.0, 1050.0, CURRENT_TIMESTAMP - INTERVAL '1 day'),
(1, CURRENT_DATE, 1050.0, 100.0, 50.0, 1100.0, CURRENT_TIMESTAMP),
-- Mouvements pour Stock Bas
(2, CURRENT_DATE - INTERVAL '2 days', 400.0, 150.0, 50.0, 500.0, CURRENT_TIMESTAMP - INTERVAL '2 days'),
(2, CURRENT_DATE - INTERVAL '1 day', 500.0, 100.0, 80.0, 520.0, CURRENT_TIMESTAMP - INTERVAL '1 day'),
(2, CURRENT_DATE, 520.0, 80.0, 30.0, 570.0, CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;
