package com.example.stocmangementservice.services;

import com.example.stocmangementservice.dtos.StockageDto;
import com.example.stocmangementservice.model.Stockage;
import org.springframework.stereotype.Service;

@Service
public class MapperService {

    public StockageDto toDto(Stockage stockage) {
        StockageDto dto = new StockageDto();
        dto.setId(stockage.getId());
        dto.setCode(stockage.getCode());
        dto.setLibelle(stockage.getLibelle());
        // Tu peux aussi mapper les stocks si besoin
        return dto;
    }

    public Stockage toEntity(StockageDto dto) {
        Stockage stockage = new Stockage();
        stockage.setId(dto.getId());
        stockage.setCode(dto.getCode());
        stockage.setLibelle(dto.getLibelle());
        return stockage;
    }
}
