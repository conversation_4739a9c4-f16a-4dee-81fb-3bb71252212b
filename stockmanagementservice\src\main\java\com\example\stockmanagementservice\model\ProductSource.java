package com.example.stockmanagementservice.model;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.*;

@Getter
@Setter
@Entity
@DiscriminatorValue("SOURCE")
public class ProductSource extends Produit {

    public ProductSource() {
        super();
    }

    public ProductSource(long id) {
        super(id);
    }

    public ProductSource(long id, String libelle) {
        super(id, libelle);
    }

    public ProductSource(String libelle) {
        super(libelle);
    }

    public ProductSource(String libelle, String code) {
        super(libelle, code);
    }
} 
