<component name="libraryTable">
  <library name="Maven: org.assertj:assertj-core:3.27.3" type="java-imported" external-system-id="Maven">
    <properties groupId="org.assertj" artifactId="assertj-core" version="3.27.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>