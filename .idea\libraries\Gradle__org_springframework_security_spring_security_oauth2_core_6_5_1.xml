<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-oauth2-core:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-oauth2-core" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-core/6.5.1/d78bb9737c23883d2dddc05b98680364603e4cc7/spring-security-oauth2-core-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-core/6.5.1/f69931ede3a156d82200993a766dbcc10295cfd3/spring-security-oauth2-core-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>