<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id="stockmangementservice:main" external.linked.project.path="$MODULE_DIR$/../../stockmangementservice" external.root.project.path="$MODULE_DIR$/../../stockmangementservice" external.system.id="GRADLE" external.system.module.group="com.example" external.system.module.type="sourceSet" external.system.module.version="0.0.1-SNAPSHOT" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet external-system-id="GRADLE" type="web" name="Web">
      <configuration>
        <webroots />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_21">
    <output url="file://$MODULE_DIR$/../../stockmangementservice/build/classes/java/main" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../stockmangementservice/src/main">
      <sourceFolder url="file://$MODULE_DIR$/../../stockmangementservice/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/../../stockmangementservice/src/main/resources" type="java-resource" />
    </content>
    <orderEntry type="jdk" jdkName="21" jdkType="JavaSDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="PROVIDED" name="Gradle: org.projectlombok:lombok:1.18.38" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-data-jpa:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-oauth2-client:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-security:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-starter-gateway-server-webflux:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-starter-gateway-server-webmvc:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-validation:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-web-services:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-starter-netflix-eureka-server:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-web:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-jdbc:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.hibernate.orm:hibernate-core:6.6.18.Final" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.data:spring-data-jpa:3.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-aspects:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-config:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-oauth2-client:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-oauth2-jose:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-core:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-oauth2-resource-server:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-web:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-aop:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-gateway-server-webflux:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-webflux:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-starter:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-properties-migrator:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-gateway-server-webmvc:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.tomcat.embed:tomcat-embed-el:10.1.42" level="project" />
    <orderEntry type="library" name="Gradle: org.hibernate.validator:hibernate-validator:8.0.2.Final" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.ws:spring-ws-core:4.1.0" level="project" />
    <orderEntry type="library" name="Gradle: com.sun.xml.messaging.saaj:saaj-impl:3.0.4" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.xml.ws:jakarta.xml.ws-api:4.0.2" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-oxm:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-netflix-eureka-server:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-starter-loadbalancer:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jaxb:jaxb-runtime:4.0.5" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-json:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-webmvc:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-tomcat:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-web:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-netflix-eureka-client:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: com.netflix.eureka:eureka-client:2.0.4" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-jdbc:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: com.zaxxer:HikariCP:6.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-autoconfigure:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-logging:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.annotation:jakarta.annotation-api:2.1.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-core:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.yaml:snakeyaml:2.4" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.persistence:jakarta.persistence-api:3.1.0" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.transaction:jakarta.transaction-api:2.0.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-context:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-orm:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.data:spring-data-commons:3.5.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-tx:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-beans:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.antlr:antlr4-runtime:4.13.0" level="project" />
    <orderEntry type="library" name="Gradle: org.slf4j:slf4j-api:2.0.17" level="project" />
    <orderEntry type="library" name="Gradle: org.aspectj:aspectjweaver:1.9.24" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-oauth2-core:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: com.nimbusds:oauth2-oidc-sdk:9.43.6" level="project" />
    <orderEntry type="library" name="Gradle: com.nimbusds:nimbus-jose-jwt:9.37.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-expression:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.security:spring-security-crypto:6.5.1" level="project" />
    <orderEntry type="library" name="Gradle: io.micrometer:micrometer-observation:1.15.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-gateway-server:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-webflux:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-reactor-netty:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-context:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-commons:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: org.bouncycastle:bcprov-jdk18on:1.80" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-gateway-server-mvc:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.validation:jakarta.validation-api:3.0.2" level="project" />
    <orderEntry type="library" name="Gradle: org.jboss.logging:jboss-logging:3.6.1.Final" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml:classmate:1.7.0" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.ws:spring-xml:4.1.0" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.xml.soap:jakarta.xml.soap-api:3.0.2" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.xml.bind:jakarta.xml.bind-api:4.0.2" level="project" />
    <orderEntry type="library" name="Gradle: org.jvnet.staxex:stax-ex:2.1.0" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.activation:jakarta.activation-api:2.1.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-actuator:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-freemarker:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.hk2:spring-bridge:3.1.1" level="project" />
    <orderEntry type="library" name="Gradle: com.netflix.eureka:eureka-core-jersey3:2.0.4" level="project" />
    <orderEntry type="library" name="Gradle: com.netflix.eureka:eureka-client-jersey3:2.0.4" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.containers:jersey-container-servlet:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.core:jersey-server:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.core:jersey-client:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.inject:jersey-hk2:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-starter-cache:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.cloud:spring-cloud-loadbalancer:4.3.0" level="project" />
    <orderEntry type="library" name="Gradle: com.stoyanr:evictor:1.0.0" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jaxb:jaxb-core:4.0.5" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.core:jackson-databind:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.tomcat.embed:tomcat-embed-websocket:10.1.42" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.tomcat.embed:tomcat-embed-core:10.1.42" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.httpcomponents.client5:httpclient5:5.5" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.core:jackson-annotations:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.jackson.core:jackson-core:2.19.1" level="project" />
    <orderEntry type="library" name="Gradle: com.thoughtworks.xstream:xstream:1.4.20" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.ws.rs:jakarta.ws.rs-api:3.1.0" level="project" />
    <orderEntry type="library" name="Gradle: jakarta.inject:jakarta.inject-api:2.0.1" level="project" />
    <orderEntry type="library" name="Gradle: com.netflix.spectator:spectator-api:1.7.3" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.httpcomponents:httpclient:4.5.14" level="project" />
    <orderEntry type="library" name="Gradle: commons-configuration:commons-configuration:1.10" level="project" />
    <orderEntry type="library" name="Gradle: ch.qos.logback:logback-classic:1.5.18" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.logging.log4j:log4j-to-slf4j:2.24.3" level="project" />
    <orderEntry type="library" name="Gradle: org.slf4j:jul-to-slf4j:2.0.17" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-jcl:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: com.github.stephenc.jcip:jcip-annotations:1.0-1" level="project" />
    <orderEntry type="library" name="Gradle: com.nimbusds:content-type:2.2" level="project" />
    <orderEntry type="library" name="Gradle: net.minidev:json-smart:2.5.2" level="project" />
    <orderEntry type="library" name="Gradle: com.nimbusds:lang-tag:1.7" level="project" />
    <orderEntry type="library" name="Gradle: io.micrometer:micrometer-commons:1.15.1" level="project" />
    <orderEntry type="library" name="Gradle: io.projectreactor.addons:reactor-extra:3.5.2" level="project" />
    <orderEntry type="library" name="Gradle: io.projectreactor:reactor-core:3.7.7" level="project" />
    <orderEntry type="library" name="Gradle: io.projectreactor.netty:reactor-netty-http:1.2.7" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-actuator-autoconfigure:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: io.micrometer:micrometer-jakarta9:1.15.1" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework:spring-context-support:6.2.8" level="project" />
    <orderEntry type="library" name="Gradle: org.freemarker:freemarker:2.3.34" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.hk2:hk2-api:3.1.1" level="project" />
    <orderEntry type="library" name="Gradle: com.netflix.eureka:eureka-core:2.0.4" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.connectors:jersey-apache-connector:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jersey.core:jersey-common:3.1.10" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.hk2:hk2-locator:3.0.6" level="project" />
    <orderEntry type="library" name="Gradle: org.javassist:javassist:3.30.2-GA" level="project" />
    <orderEntry type="library" name="Gradle: com.fasterxml.woodstox:woodstox-core:7.1.1" level="project" />
    <orderEntry type="library" name="Gradle: org.codehaus.woodstox:stax2-api:4.2.2" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.jaxb:txw2:4.0.5" level="project" />
    <orderEntry type="library" name="Gradle: com.sun.istack:istack-commons-runtime:4.1.2" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.httpcomponents.core5:httpcore5-h2:5.3.4" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.httpcomponents.core5:httpcore5:5.3.4" level="project" />
    <orderEntry type="library" name="Gradle: io.github.x-stream:mxparser:1.2.2" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.httpcomponents:httpcore:4.4.16" level="project" />
    <orderEntry type="library" name="Gradle: commons-codec:commons-codec:1.18.0" level="project" />
    <orderEntry type="library" name="Gradle: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Gradle: ch.qos.logback:logback-core:1.5.18" level="project" />
    <orderEntry type="library" name="Gradle: org.apache.logging.log4j:log4j-api:2.24.3" level="project" />
    <orderEntry type="library" name="Gradle: net.minidev:accessors-smart:2.5.2" level="project" />
    <orderEntry type="library" name="Gradle: org.reactivestreams:reactive-streams:1.0.4" level="project" />
    <orderEntry type="library" name="Gradle: io.projectreactor.netty:reactor-netty-core:1.2.7" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-codec-http2:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-codec-http:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-resolver-dns-native-macos:osx-x86_64:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-resolver-dns:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-transport-native-epoll:linux-x86_64:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: org.springframework.boot:spring-boot-actuator:3.5.3" level="project" />
    <orderEntry type="library" name="Gradle: io.micrometer:micrometer-core:1.15.1" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.hk2:hk2-utils:3.1.1" level="project" />
    <orderEntry type="library" name="Gradle: org.glassfish.hk2:osgi-resource-locator:1.0.3" level="project" />
    <orderEntry type="library" name="Gradle: xmlpull:xmlpull:1.1.3.1" level="project" />
    <orderEntry type="library" name="Gradle: org.ow2.asm:asm:9.7.1" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-handler-proxy:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-handler:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-codec:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-transport:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-buffer:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-common:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-resolver-dns-classes-macos:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-codec-dns:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-resolver:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-transport-classes-epoll:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-transport-native-unix-common:4.1.122.Final" level="project" />
    <orderEntry type="library" name="Gradle: io.netty:netty-codec-socks:4.1.122.Final" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.postgresql:postgresql:42.7.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.checkerframework:checker-qual:3.49.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.hibernate.common:hibernate-commons-annotations:7.0.3.Final" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: io.smallrye:jandex:3.2.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: net.bytebuddy:byte-buddy:1.17.6" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.springframework.boot:spring-boot-configuration-metadata:3.5.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.eclipse.angus:angus-activation:2.0.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.netflix.netflix-commons:netflix-eventbus:0.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: javax.annotation:javax.annotation-api:1.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.codehaus.jettison:jettison:1.5.4" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.netflix.netflix-commons:netflix-infix:0.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.netflix.servo:servo-core:0.5.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.apache.commons:commons-math:2.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.google.guava:guava:33.0.0-jre" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: commons-jxpath:commons-jxpath:1.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: joda-time:joda-time:2.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.antlr:antlr-runtime:3.4" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.google.code.gson:gson:2.13.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.hdrhistogram:HdrHistogram:2.2.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.latencyutils:LatencyUtils:2.0.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.google.errorprone:error_prone_annotations:2.38.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.google.guava:failureaccess:1.0.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: org.antlr:stringtemplate:3.2.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Gradle: antlr:antlr:2.7.7" level="project" />
  </component>
</module>