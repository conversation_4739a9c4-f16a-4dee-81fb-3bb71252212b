package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.ProductMarchandDto;
import com.example.stockmanagementservice.model.ProductMarchand;
import com.example.stockmanagementservice.repositories.ProductMarchandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des produits marchands - CRUD de base
 */
@Service
public class ProductMarchandService {
    
    @Autowired
    private ProductMarchandRepository productMarchandRepository;
    
    // Créer un produit marchand
    public ProductMarchand createProductMarchand(ProductMarchand productMarchand) {
        return productMarchandRepository.save(productMarchand);
    }
    
    // Récupérer tous les produits marchands
    public List<ProductMarchand> getAllProductMarchands() {
        return productMarchandRepository.findAll();
    }
    
    // Récupérer un produit marchand par ID
    public Optional<ProductMarchand> getProductMarchandById(Long id) {
        return productMarchandRepository.findById(id);
    }
    
    // Récupérer un produit marchand par code
    public Optional<ProductMarchand> getProductMarchandByCode(String code) {
        return productMarchandRepository.findByCode(code);
    }
    
    // Rechercher par libellé
    public List<ProductMarchand> searchByLibelle(String libelle) {
        return productMarchandRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    // Mettre à jour un produit marchand
    public ProductMarchand updateProductMarchand(Long id, ProductMarchand productMarchandDetails) {
        Optional<ProductMarchand> optionalProductMarchand = productMarchandRepository.findById(id);
        if (optionalProductMarchand.isPresent()) {
            ProductMarchand productMarchand = optionalProductMarchand.get();
            productMarchand.setCode(productMarchandDetails.getCode());
            productMarchand.setLibelle(productMarchandDetails.getLibelle());
            productMarchand.setDescription(productMarchandDetails.getDescription());
            return productMarchandRepository.save(productMarchand);
        }
        return null;
    }
    
    // Supprimer un produit marchand
    public boolean deleteProductMarchand(Long id) {
        if (productMarchandRepository.existsById(id)) {
            productMarchandRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Produits ordonnés par libellé
    public List<ProductMarchand> getProductMarchandsOrderedByLibelle() {
        return productMarchandRepository.findAllByOrderByLibelleAsc();
    }
    
    // Convertir entité vers DTO
    public ProductMarchandDto convertToDto(ProductMarchand productMarchand) {
        return new ProductMarchandDto(
            productMarchand.getId(),
            productMarchand.getCode(),
            productMarchand.getLibelle(),
            productMarchand.getDescription()
        );
    }
    
    // Convertir DTO vers entité
    public ProductMarchand convertToEntity(ProductMarchandDto dto) {
        ProductMarchand productMarchand = new ProductMarchand();
        productMarchand.setId(dto.getId());
        productMarchand.setCode(dto.getCode());
        productMarchand.setLibelle(dto.getLibelle());
        productMarchand.setDescription(dto.getDescription());
        return productMarchand;
    }
}
