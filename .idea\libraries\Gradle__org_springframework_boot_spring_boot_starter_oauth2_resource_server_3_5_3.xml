<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-oauth2-resource-server" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-oauth2-resource-server/3.5.3/d17f1ee0ef4b2ec1681e01aa61c402ea78ce2619/spring-boot-starter-oauth2-resource-server-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-oauth2-resource-server/3.5.3/d17f1ee0ef4b2ec1681e01aa61c402ea78ce2619/spring-boot-starter-oauth2-resource-server-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>