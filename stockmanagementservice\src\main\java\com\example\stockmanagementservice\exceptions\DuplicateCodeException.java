package com.example.stockmanagementservice.exceptions;

/**
 * Exception levée quand on tente de créer un élément avec un code déjà existant
 */
public class DuplicateCodeException extends StockException {
    
    public DuplicateCodeException(String entityType, String code) {
        super(String.format("Un %s avec le code '%s' existe déjà", entityType, code));
    }
    
    public DuplicateCodeException(String message) {
        super(message);
    }
    
    public DuplicateCodeException(String message, Throwable cause) {
        super(message, cause);
    }
} 
