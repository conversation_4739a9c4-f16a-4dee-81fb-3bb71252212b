package com.example.stocmangementservice.repositories;

import com.example.stocmangementservice.model.Produit;
import com.example.stocmangementservice.model.Stock;
import com.example.stocmangementservice.model.StockProduit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité StockProduit (relation Stock-Produit)
 */
@Repository
public interface StockProduitRepository extends JpaRepository<StockProduit, Long> {
    
    /**
     * Recherche par stock
     */
    List<StockProduit> findByStock(Stock stock);
    
    /**
     * Recherche par produit
     */
    List<StockProduit> findByProduit(Produit produit);
    
    /**
     * Recherche par IDs de stock et produit
     */
    Optional<StockProduit> findByStockIdAndProduitId(Long stockId, Long produitId);
    
    /**
     * Vérifier l'existence d'une relation stock-produit
     */
    boolean existsByStockIdAndProduitId(Long stockId, Long produitId);
    
    /**
     * Recherche par stock et produit
     */
    Optional<StockProduit> findByStockAndProduit(Stock stock, Produit produit);
    
    /**
     * Recherche par ID de stock
     */
    List<StockProduit> findByStockId(Long stockId);
    
    /**
     * Recherche par ID de produit
     */
    List<StockProduit> findByProduitId(Long produitId);
    
    /**
     * StockProduits ayant des suivis journaliers
     */
    @Query("SELECT DISTINCT sp FROM StockProduit sp JOIN sp.stockJournaliers sj")
    List<StockProduit> findStockProduitsWithJournaliers();
    
    /**
     * StockProduits sans suivis journaliers
     */
    @Query("SELECT sp FROM StockProduit sp WHERE sp.stockJournaliers IS EMPTY")
    List<StockProduit> findStockProduitsSansSuivi();
    
    /**
     * Recherche par code de stock
     */
    @Query("SELECT sp FROM StockProduit sp WHERE sp.stock.code = :stockCode")
    List<StockProduit> findByStockCode(@Param("stockCode") String stockCode);
    
    /**
     * Recherche par code de produit
     */
    @Query("SELECT sp FROM StockProduit sp WHERE sp.produit.code = :produitCode")
    List<StockProduit> findByProduitCode(@Param("produitCode") String produitCode);
    
    /**
     * Recherche multicritères avec pagination
     */
    @Query("SELECT sp FROM StockProduit sp WHERE " +
           "(:stockId IS NULL OR sp.stock.id = :stockId) AND " +
           "(:produitId IS NULL OR sp.produit.id = :produitId) AND " +
           "(:stockCode IS NULL OR LOWER(sp.stock.code) LIKE LOWER(CONCAT('%', :stockCode, '%'))) AND " +
           "(:produitCode IS NULL OR LOWER(sp.produit.code) LIKE LOWER(CONCAT('%', :produitCode, '%')))")
    Page<StockProduit> findBySearchCriteria(
        @Param("stockId") Long stockId,
        @Param("produitId") Long produitId,
        @Param("stockCode") String stockCode,
        @Param("produitCode") String produitCode,
        Pageable pageable
    );
    
    /**
     * Compter les suivis journaliers par stock-produit
     */
    @Query("SELECT sp, COUNT(sj) FROM StockProduit sp LEFT JOIN sp.stockJournaliers sj GROUP BY sp")
    List<Object[]> countJournaliersByStockProduit();
    
    /**
     * Recherche par stockage (via stock)
     */
    @Query("SELECT sp FROM StockProduit sp WHERE sp.stock.stockage.id = :stockageId")
    List<StockProduit> findByStockageId(@Param("stockageId") Long stockageId);
} 