<component name="libraryTable">
  <library name="Maven: org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springdoc" artifactId="springdoc-openapi-starter-webmvc-ui" version="2.2.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>