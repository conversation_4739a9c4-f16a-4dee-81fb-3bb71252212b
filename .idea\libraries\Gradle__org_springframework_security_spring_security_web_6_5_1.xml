<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-web:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-web" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-web/6.5.1/9255d45a0f69305ceae1fb770a774acdf805c485/spring-security-web-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-web/6.5.1/d99d119b3b690acd60963ce757802102fc4ac217/spring-security-web-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>