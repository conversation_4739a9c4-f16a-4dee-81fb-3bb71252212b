package com.example.stocmangementservice.exceptions;

/**
 * Exception levée quand un stock n'est pas trouvé
 */
public class StockNotFoundException extends StockException {
    
    public StockNotFoundException(Long id) {
        super("Stock avec l'ID " + id + " non trouvé");
    }
    
    public StockNotFoundException(String code) {
        super("Stock avec le code " + code + " non trouvé");
    }
    
    public StockNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
} 