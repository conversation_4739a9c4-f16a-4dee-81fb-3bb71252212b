<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-freemarker:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-freemarker" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-freemarker/3.5.3/672d9d4a5b32f4fcf3ede11d363614208b63e464/spring-boot-starter-freemarker-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-freemarker/3.5.3/672d9d4a5b32f4fcf3ede11d363614208b63e464/spring-boot-starter-freemarker-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>