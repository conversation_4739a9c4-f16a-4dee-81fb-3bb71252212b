<component name="libraryTable">
  <library name="Maven: commons-configuration:commons-configuration:1.10" type="java-imported" external-system-id="Maven">
    <properties groupId="commons-configuration" artifactId="commons-configuration" version="1.10" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/commons-configuration/commons-configuration/1.10/commons-configuration-1.10-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/commons-configuration/commons-configuration/1.10/commons-configuration-1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>