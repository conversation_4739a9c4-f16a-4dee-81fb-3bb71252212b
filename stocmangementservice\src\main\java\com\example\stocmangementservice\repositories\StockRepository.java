package com.example.stocmangementservice.repositories;

import com.example.stocmangementservice.model.Stock;
import com.example.stocmangementservice.model.Stockage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité Stock
 */
@Repository
public interface StockRepository extends JpaRepository<Stock, Long> {
    
    /**
     * Recherche par code (unique)
     */
    Optional<Stock> findByCode(String code);
    
    /**
     * Vérifier l'existence par code
     */
    boolean existsByCode(String code);
    
    /**
     * Recherche par libellé (contient, insensible à la casse)
     */
    List<Stock> findByLibelleContainingIgnoreCase(String libelle);
    
    /**
     * Recherche par stockage
     */
    List<Stock> findByStockage(Stockage stockage);
    
    /**
     * Recherche par ID de stockage
     */
    List<Stock> findByStockageId(Long stockageId);
    
    /**
     * Recherche multicritères avec pagination
     */
    @Query("SELECT s FROM Stock s WHERE " +
           "(:code IS NULL OR LOWER(s.code) LIKE LOWER(CONCAT('%', :code, '%'))) AND " +
           "(:libelle IS NULL OR LOWER(s.libelle) LIKE LOWER(CONCAT('%', :libelle, '%'))) AND " +
           "(:stockageId IS NULL OR s.stockage.id = :stockageId)")
    Page<Stock> findBySearchCriteria(
        @Param("code") String code,
        @Param("libelle") String libelle,
        @Param("stockageId") Long stockageId,
        Pageable pageable
    );
    
    /**
     * Recherche multicritères sans pagination
     */
    @Query("SELECT s FROM Stock s WHERE " +
           "(:code IS NULL OR LOWER(s.code) LIKE LOWER(CONCAT('%', :code, '%'))) AND " +
           "(:libelle IS NULL OR LOWER(s.libelle) LIKE LOWER(CONCAT('%', :libelle, '%'))) AND " +
           "(:stockageId IS NULL OR s.stockage.id = :stockageId)")
    List<Stock> findBySearchCriteria(
        @Param("code") String code,
        @Param("libelle") String libelle,
        @Param("stockageId") Long stockageId
    );
    
    /**
     * Stocks ayant des produits associés
     */
    @Query("SELECT DISTINCT s FROM Stock s JOIN s.stockProduits sp")
    List<Stock> findStocksWithProducts();
    
    /**
     * Stocks sans produits
     */
    @Query("SELECT s FROM Stock s WHERE s.stockProduits IS EMPTY")
    List<Stock> findStocksSansProduits();
    
    /**
     * Compter le nombre de produits par stock
     */
    @Query("SELECT s, COUNT(sp) FROM Stock s LEFT JOIN s.stockProduits sp GROUP BY s")
    List<Object[]> countProductsByStock();
    
    /**
     * Stocks par stockage avec pagination
     */
    Page<Stock> findByStockage(Stockage stockage, Pageable pageable);
    
    /**
     * Recherche par code de stockage
     */
    @Query("SELECT s FROM Stock s WHERE s.stockage.code = :stockageCode")
    List<Stock> findByStockageCode(@Param("stockageCode") String stockageCode);
    
    /**
     * Stocks ordonnés par libellé
     */
    List<Stock> findAllByOrderByLibelleAsc();
} 