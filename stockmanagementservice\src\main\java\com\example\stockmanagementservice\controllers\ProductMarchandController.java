package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.ProductMarchandDto;
import com.example.stockmanagementservice.model.ProductMarchand;
import com.example.stockmanagementservice.services.ProductMarchandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des produits marchands
 */
@RestController
@RequestMapping("/api/product-marchands")
public class ProductMarchandController {
    
    @Autowired
    private ProductMarchandService productMarchandService;
    
    // Créer un produit marchand
    @PostMapping
    public ResponseEntity<ProductMarchandDto> createProductMarchand(@RequestBody ProductMarchandDto productMarchandDto) {
        try {
            ProductMarchand productMarchand = productMarchandService.convertToEntity(productMarchandDto);
            ProductMarchand savedProductMarchand = productMarchandService.createProductMarchand(productMarchand);
            ProductMarchandDto responseDto = productMarchandService.convertToDto(savedProductMarchand);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer tous les produits marchands
    @GetMapping
    public ResponseEntity<List<ProductMarchandDto>> getAllProductMarchands() {
        try {
            List<ProductMarchand> productMarchands = productMarchandService.getAllProductMarchands();
            List<ProductMarchandDto> productMarchandsDto = productMarchands.stream()
                .map(productMarchandService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(productMarchandsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un produit marchand par ID
    @GetMapping("/{id}")
    public ResponseEntity<ProductMarchandDto> getProductMarchandById(@PathVariable Long id) {
        try {
            Optional<ProductMarchand> productMarchand = productMarchandService.getProductMarchandById(id);
            if (productMarchand.isPresent()) {
                ProductMarchandDto productMarchandDto = productMarchandService.convertToDto(productMarchand.get());
                return new ResponseEntity<>(productMarchandDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un produit marchand par code
    @GetMapping("/code/{code}")
    public ResponseEntity<ProductMarchandDto> getProductMarchandByCode(@PathVariable String code) {
        try {
            Optional<ProductMarchand> productMarchand = productMarchandService.getProductMarchandByCode(code);
            if (productMarchand.isPresent()) {
                ProductMarchandDto productMarchandDto = productMarchandService.convertToDto(productMarchand.get());
                return new ResponseEntity<>(productMarchandDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Rechercher par libellé
    @GetMapping("/search")
    public ResponseEntity<List<ProductMarchandDto>> searchProductMarchands(@RequestParam String libelle) {
        try {
            List<ProductMarchand> productMarchands = productMarchandService.searchByLibelle(libelle);
            List<ProductMarchandDto> productMarchandsDto = productMarchands.stream()
                .map(productMarchandService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(productMarchandsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les produits marchands ordonnés par libellé
    @GetMapping("/ordered")
    public ResponseEntity<List<ProductMarchandDto>> getProductMarchandsOrdered() {
        try {
            List<ProductMarchand> productMarchands = productMarchandService.getProductMarchandsOrderedByLibelle();
            List<ProductMarchandDto> productMarchandsDto = productMarchands.stream()
                .map(productMarchandService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(productMarchandsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour un produit marchand
    @PutMapping("/{id}")
    public ResponseEntity<ProductMarchandDto> updateProductMarchand(@PathVariable Long id, @RequestBody ProductMarchandDto productMarchandDto) {
        try {
            ProductMarchand productMarchandDetails = productMarchandService.convertToEntity(productMarchandDto);
            ProductMarchand updatedProductMarchand = productMarchandService.updateProductMarchand(id, productMarchandDetails);
            if (updatedProductMarchand != null) {
                ProductMarchandDto responseDto = productMarchandService.convertToDto(updatedProductMarchand);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer un produit marchand
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteProductMarchand(@PathVariable Long id) {
        try {
            boolean deleted = productMarchandService.deleteProductMarchand(id);
            if (deleted) {
                return new ResponseEntity<>("Produit marchand supprimé avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Produit marchand non trouvé", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
