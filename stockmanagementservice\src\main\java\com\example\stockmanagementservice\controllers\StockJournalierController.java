package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.StockJournalierDto;
import com.example.stockmanagementservice.model.StockJournalier;
import com.example.stockmanagementservice.services.StockJournalierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des stocks journaliers
 */
@RestController
@RequestMapping("/api/stock-journaliers")
public class StockJournalierController {
    
    @Autowired
    private StockJournalierService stockJournalierService;
    
    // Créer un stock journalier
    @PostMapping
    public ResponseEntity<StockJournalierDto> createStockJournalier(@RequestBody StockJournalierDto stockJournalierDto) {
        try {
            StockJournalier stockJournalier = stockJournalierService.convertToEntity(stockJournalierDto);
            StockJournalier savedStockJournalier = stockJournalierService.createStockJournalier(stockJournalier);
            StockJournalierDto responseDto = stockJournalierService.convertToDto(savedStockJournalier);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer tous les stocks journaliers
    @GetMapping
    public ResponseEntity<List<StockJournalierDto>> getAllStockJournaliers() {
        try {
            List<StockJournalier> stockJournaliers = stockJournalierService.getAllStockJournaliers();
            List<StockJournalierDto> stockJournaliersDto = stockJournaliers.stream()
                .map(stockJournalierService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockJournaliersDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un stock journalier par ID
    @GetMapping("/{id}")
    public ResponseEntity<StockJournalierDto> getStockJournalierById(@PathVariable Long id) {
        try {
            Optional<StockJournalier> stockJournalier = stockJournalierService.getStockJournalierById(id);
            if (stockJournalier.isPresent()) {
                StockJournalierDto stockJournalierDto = stockJournalierService.convertToDto(stockJournalier.get());
                return new ResponseEntity<>(stockJournalierDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks journaliers par date
    @GetMapping("/date/{date}")
    public ResponseEntity<List<StockJournalierDto>> getStockJournaliersByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            List<StockJournalier> stockJournaliers = stockJournalierService.getStockJournaliersByDate(date);
            List<StockJournalierDto> stockJournaliersDto = stockJournaliers.stream()
                .map(stockJournalierService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockJournaliersDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks journaliers par période
    @GetMapping("/period")
    public ResponseEntity<List<StockJournalierDto>> getStockJournaliersByPeriod(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            List<StockJournalier> stockJournaliers = stockJournalierService.getStockJournaliersByPeriod(startDate, endDate);
            List<StockJournalierDto> stockJournaliersDto = stockJournaliers.stream()
                .map(stockJournalierService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockJournaliersDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks journaliers par stock
    @GetMapping("/stock/{stockId}")
    public ResponseEntity<List<StockJournalierDto>> getStockJournaliersByStock(@PathVariable Long stockId) {
        try {
            List<StockJournalier> stockJournaliers = stockJournalierService.getStockJournaliersByStock(stockId);
            List<StockJournalierDto> stockJournaliersDto = stockJournaliers.stream()
                .map(stockJournalierService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockJournaliersDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks journaliers par produit
    @GetMapping("/produit/{produitId}")
    public ResponseEntity<List<StockJournalierDto>> getStockJournaliersByProduit(@PathVariable Long produitId) {
        try {
            List<StockJournalier> stockJournaliers = stockJournalierService.getStockJournaliersByProduit(produitId);
            List<StockJournalierDto> stockJournaliersDto = stockJournaliers.stream()
                .map(stockJournalierService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockJournaliersDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks journaliers ordonnés par date
    @GetMapping("/ordered")
    public ResponseEntity<List<StockJournalierDto>> getStockJournaliersOrdered() {
        try {
            List<StockJournalier> stockJournaliers = stockJournalierService.getStockJournaliersOrderedByDate();
            List<StockJournalierDto> stockJournaliersDto = stockJournaliers.stream()
                .map(stockJournalierService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockJournaliersDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour un stock journalier
    @PutMapping("/{id}")
    public ResponseEntity<StockJournalierDto> updateStockJournalier(@PathVariable Long id, @RequestBody StockJournalierDto stockJournalierDto) {
        try {
            StockJournalier stockJournalierDetails = stockJournalierService.convertToEntity(stockJournalierDto);
            StockJournalier updatedStockJournalier = stockJournalierService.updateStockJournalier(id, stockJournalierDetails);
            if (updatedStockJournalier != null) {
                StockJournalierDto responseDto = stockJournalierService.convertToDto(updatedStockJournalier);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer un stock journalier
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteStockJournalier(@PathVariable Long id) {
        try {
            boolean deleted = stockJournalierService.deleteStockJournalier(id);
            if (deleted) {
                return new ResponseEntity<>("Stock journalier supprimé avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Stock journalier non trouvé", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
