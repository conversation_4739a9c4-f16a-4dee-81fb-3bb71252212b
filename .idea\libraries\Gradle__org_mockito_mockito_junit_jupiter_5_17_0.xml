<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-junit-jupiter:5.17.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.mockito" artifactId="mockito-junit-jupiter" version="5.17.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/5.17.0/4399e19e3fb5b55230027583323e4c883d5da07d/mockito-junit-jupiter-5.17.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/5.17.0/2845f96e486603334a5bcb97e8e97d877e70cb47/mockito-junit-jupiter-5.17.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>