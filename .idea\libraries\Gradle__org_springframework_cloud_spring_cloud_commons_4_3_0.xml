<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-commons:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-commons" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-commons/4.3.0/e939caa4370105f53b1213520692615bed7605f3/spring-cloud-commons-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-commons/4.3.0/7a442ab245dcd0f714403e9950cce00aa273e273/spring-cloud-commons-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>