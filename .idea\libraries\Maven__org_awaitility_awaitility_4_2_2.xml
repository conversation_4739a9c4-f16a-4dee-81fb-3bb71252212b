<component name="libraryTable">
  <library name="Maven: org.awaitility:awaitility:4.2.2" type="java-imported" external-system-id="Maven">
    <properties groupId="org.awaitility" artifactId="awaitility" version="4.2.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/awaitility/awaitility/4.2.2/awaitility-4.2.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/awaitility/awaitility/4.2.2/awaitility-4.2.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>