package com.example.stockmanagementservice.dtos;

import java.time.LocalDate;

/**
 * DTO simple pour les stocks journaliers
 */
public class StockJournalierDto {
    
    private Long id;
    private LocalDate date;
    private Long stockId;
    private Long produitId;
    private Integer quantite;
    
    // Constructeurs
    public StockJournalierDto() {}
    
    public StockJournalierDto(Long id, LocalDate date, Long stockId, Long produitId, Integer quantite) {
        this.id = id;
        this.date = date;
        this.stockId = stockId;
        this.produitId = produitId;
        this.quantite = quantite;
    }
    
    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public LocalDate getDate() { return date; }
    public void setDate(LocalDate date) { this.date = date; }
    
    public Long getStockId() { return stockId; }
    public void setStockId(Long stockId) { this.stockId = stockId; }
    
    public Long getProduitId() { return produitId; }
    public void setProduitId(Long produitId) { this.produitId = produitId; }
    
    public Integer getQuantite() { return quantite; }
    public void setQuantite(Integer quantite) { this.quantite = quantite; }
}
