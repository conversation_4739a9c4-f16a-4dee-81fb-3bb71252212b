package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.StockDto;
import com.example.stockmanagementservice.exceptions.DuplicateCodeException;
import com.example.stockmanagementservice.exceptions.StockNotFoundException;
import com.example.stockmanagementservice.model.Stock;
import com.example.stockmanagementservice.model.Stockage;
import com.example.stockmanagementservice.repositories.StockRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service pour la gestion des stocks
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class StockService {
    
    private final StockRepository stockRepository;
    private final StockageService stockageService;
    
    /**
     * Créer un nouveau stock
     */
    public Stock creerStock(StockDto stockDto) {
        log.info("Création d'un nouveau stock : {}", stockDto.getCode());
        
        // Vérifier l'unicité du code
        if (stockRepository.existsByCode(stockDto.getCode())) {
            throw new DuplicateCodeException("Stock", stockDto.getCode());
        }
        
        // Récupérer le stockage parent
        Stockage stockage = stockageService.findById(stockDto.getStockageId());
        
        Stock stock = new Stock();
        stock.setCode(stockDto.getCode());
        stock.setLibelle(stockDto.getLibelle());
        stock.setStockage(stockage);
        
        stock = stockRepository.save(stock);
        log.info("Stock créé avec succès : ID={}, Code={}", stock.getId(), stock.getCode());
        
        return stock;
    }
    
    /**
     * Rechercher un stock par ID
     */
    @Transactional(readOnly = true)
    public Stock findById(Long id) {
        return stockRepository.findById(id)
                .orElseThrow(() -> new StockNotFoundException(id));
    }
    
    /**
     * Rechercher un stock par code
     */
    @Transactional(readOnly = true)
    public Stock findByCode(String code) {
        return stockRepository.findByCode(code)
                .orElseThrow(() -> new StockNotFoundException(code));
    }
    
    /**
     * Rechercher tous les stocks avec pagination
     */
    @Transactional(readOnly = true)
    public Page<Stock> findAll(Pageable pageable) {
        return stockRepository.findAll(pageable);
    }
    
    /**
     * Rechercher tous les stocks (sans pagination)
     */
    @Transactional(readOnly = true)
    public List<Stock> findAll() {
        return stockRepository.findAll();
    }
    
    /**
     * Rechercher les stocks par stockage
     */
    @Transactional(readOnly = true)
    public List<Stock> findByStockage(Stockage stockage) {
        return stockRepository.findByStockage(stockage);
    }
    
    /**
     * Rechercher les stocks par ID de stockage
     */
    @Transactional(readOnly = true)
    public List<Stock> findByStockageId(Long stockageId) {
        return stockRepository.findByStockageId(stockageId);
    }
    
    /**
     * Rechercher par libellé contenant
     */
    @Transactional(readOnly = true)
    public List<Stock> findByLibelleContaining(String libelle) {
        return stockRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    /**
     * Recherche multicritères
     */
    @Transactional(readOnly = true)
    public List<Stock> rechercherStocks(String code, String libelle, Long stockageId) {
        return stockRepository.findBySearchCriteria(code, libelle, stockageId);
    }
    
    /**
     * Recherche multicritères avec pagination
     */
    @Transactional(readOnly = true)
    public Page<Stock> rechercherStocks(String code, String libelle, Long stockageId, Pageable pageable) {
        return stockRepository.findBySearchCriteria(code, libelle, stockageId, pageable);
    }
    
    /**
     * Mettre à jour un stock
     */
    public Stock modifierStock(Long id, StockDto stockDto) {
        log.info("Mise à jour du stock ID={}", id);
        
        Stock stock = findById(id);
        
        // Vérifier l'unicité du code si changé
        if (!stock.getCode().equals(stockDto.getCode()) && 
            stockRepository.existsByCode(stockDto.getCode())) {
            throw new DuplicateCodeException("Stock", stockDto.getCode());
        }
        
        // Mettre à jour le stockage si nécessaire
        if (!stock.getStockage().getId().equals(stockDto.getStockageId())) {
            Stockage newStockage = stockageService.findById(stockDto.getStockageId());
            stock.setStockage(newStockage);
        }
        
        stock.setCode(stockDto.getCode());
        stock.setLibelle(stockDto.getLibelle());
        
        stock = stockRepository.save(stock);
        log.info("Stock mis à jour avec succès : ID={}", id);
        
        return stock;
    }
    
    /**
     * Supprimer un stock
     */
    public void supprimerStock(Long id) {
        log.info("Suppression du stock ID={}", id);
        
        Stock stock = findById(id);
        
        // Vérifier s'il n'y a pas de produits associés
        if (stock.getStockProduits() != null && !stock.getStockProduits().isEmpty()) {
            throw new IllegalStateException("Impossible de supprimer un stock ayant des produits associés");
        }
        
        stockRepository.deleteById(id);
        log.info("Stock supprimé avec succès : ID={}", id);
    }
    
    /**
     * Rechercher les stocks ayant des produits
     */
    @Transactional(readOnly = true)
    public List<Stock> getStocksWithProducts() {
        return stockRepository.findStocksWithProducts();
    }
    
    /**
     * Rechercher les stocks sans produits
     */
    @Transactional(readOnly = true)
    public List<Stock> getStocksSansProducts() {
        return stockRepository.findStocksSansProduits();
    }
    
    /**
     * Vérifier l'existence d'un stock par code
     */
    @Transactional(readOnly = true)
    public boolean existsByCode(String code) {
        return stockRepository.existsByCode(code);
    }
    
    /**
     * Compter le nombre de produits par stock
     */
    @Transactional(readOnly = true)
    public List<Object[]> countProductsByStock() {
        return stockRepository.countProductsByStock();
    }
    
    /**
     * Rechercher par code de stockage
     */
    @Transactional(readOnly = true)
    public List<Stock> findByStockageCode(String stockageCode) {
        return stockRepository.findByStockageCode(stockageCode);
    }
    
    /**
     * Rechercher tous les stocks ordonnés par libellé
     */
    @Transactional(readOnly = true)
    public List<Stock> findAllOrderByLibelle() {
        return stockRepository.findAllByOrderByLibelleAsc();
    }
} 
