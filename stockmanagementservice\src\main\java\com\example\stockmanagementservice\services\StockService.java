package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.StockDto;
import com.example.stockmanagementservice.model.Stock;
import com.example.stockmanagementservice.repositories.StockRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des stocks - CRUD de base
 */
@Service
public class StockService {

    @Autowired
    private StockRepository stockRepository;

    // Créer un stock
    public Stock createStock(Stock stock) {
        return stockRepository.save(stock);
    }

    // Récupérer tous les stocks
    public List<Stock> getAllStocks() {
        return stockRepository.findAll();
    }

    // Récupérer un stock par ID
    public Optional<Stock> getStockById(Long id) {
        return stockRepository.findById(id);
    }

    // Récupérer un stock par code
    public Optional<Stock> getStockByCode(String code) {
        return stockRepository.findByCode(code);
    }

    // Rechercher par libellé
    public List<Stock> searchByLibelle(String libelle) {
        return stockRepository.findByLibelleContainingIgnoreCase(libelle);
    }

    // Mettre à jour un stock
    public Stock updateStock(Long id, Stock stockDetails) {
        Optional<Stock> optionalStock = stockRepository.findById(id);
        if (optionalStock.isPresent()) {
            Stock stock = optionalStock.get();
            stock.setCode(stockDetails.getCode());
            stock.setLibelle(stockDetails.getLibelle());
            return stockRepository.save(stock);
        }
        return null;
    }

    // Supprimer un stock
    public boolean deleteStock(Long id) {
        if (stockRepository.existsById(id)) {
            stockRepository.deleteById(id);
            return true;
        }
        return false;
    }

    // Stocks ordonnés par libellé
    public List<Stock> getStocksOrderedByLibelle() {
        return stockRepository.findAllByOrderByLibelleAsc();
    }

    // Convertir entité vers DTO
    public StockDto convertToDto(Stock stock) {
        return new StockDto(
            stock.getId(),
            stock.getCode(),
            stock.getLibelle(),
            0 // quantité par défaut
        );
    }

    // Convertir DTO vers entité
    public Stock convertToEntity(StockDto dto) {
        Stock stock = new Stock();
        stock.setId(dto.getId());
        stock.setCode(dto.getCode());
        stock.setLibelle(dto.getLibelle());
        return stock;
    }
} 
