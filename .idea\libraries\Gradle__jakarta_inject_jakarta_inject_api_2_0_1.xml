<component name="libraryTable">
  <library name="Gradle: jakarta.inject:jakarta.inject-api:2.0.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.inject" artifactId="jakarta.inject-api" version="2.0.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.inject/jakarta.inject-api/2.0.1/4c28afe1991a941d7702fe1362c365f0a8641d1e/jakarta.inject-api-2.0.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.inject/jakarta.inject-api/2.0.1/569d6c5f18d6ff83c82152d1870cf0faf830c594/jakarta.inject-api-2.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>