spring:
  application:
    name: STOCK-MANAGEMENT-SERVICE
  
  # Configuration temporaire pour développement avec H2
  datasource:
    url: jdbc:h2:mem:stockdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/

server:
  port: 8081 