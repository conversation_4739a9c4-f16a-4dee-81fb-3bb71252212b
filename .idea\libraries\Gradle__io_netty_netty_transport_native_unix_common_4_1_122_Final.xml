<component name="libraryTable">
  <library name="Gradle: io.netty:netty-transport-native-unix-common:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-transport-native-unix-common" version="4.1.122.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.122.Final/27ce9402817d5578c6b14c31a02d781e3b4fa5b5/netty-transport-native-unix-common-4.1.122.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.122.Final/fc0bb4cbbd7ef0a4b4d48004326077127412cf47/netty-transport-native-unix-common-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>