version: '3.8'

services:
  # PostgreSQL Database for Stock Management Service
  stock-management-db:
    image: postgres:15-alpine
    container_name: stock-management-db
    environment:
      POSTGRES_DB: stock_management
      POSTGRES_USER: stock_user
      POSTGRES_PASSWORD: stock_password
    ports:
      - "5432:5432"
    volumes:
      - stock_management_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - microservices-network
    restart: unless-stopped

  # Eureka Server (Service Discovery)
  eureka-server:
    build:
      context: ./eureka-server
      dockerfile: dockerfile
    container_name: eureka-server
    ports:
      - "8761:8761"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    networks:
      - microservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: dockerfile
    container_name: api-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
    depends_on:
      eureka-server:
        condition: service_healthy
    networks:
      - microservices-network
    restart: unless-stopped

  # Stock Management Service
  stock-management-service:
    build:
      context: ./stocmangementservice
      dockerfile: dockerfile
    container_name: stock-management-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=***********************************************************
      - SPRING_DATASOURCE_USERNAME=stock_user
      - SPRING_DATASOURCE_PASSWORD=stock_password
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
    depends_on:
      stock-management-db:
        condition: service_started
      eureka-server:
        condition: service_healthy
    networks:
      - microservices-network
    restart: unless-stopped

# Future microservices template (uncomment and modify as needed)
#  # Example: User Management Service
#  user-management-db:
#    image: postgres:15-alpine
#    container_name: user-management-db
#    environment:
#      POSTGRES_DB: user_management
#      POSTGRES_USER: user_user
#      POSTGRES_PASSWORD: user_password
#    ports:
#      - "5433:5432"
#    volumes:
#      - user_management_data:/var/lib/postgresql/data
#    networks:
#      - microservices-network
#    restart: unless-stopped
#
#  user-management-service:
#    build:
#      context: ./user-management-service
#      dockerfile: dockerfile
#    container_name: user-management-service
#    ports:
#      - "8082:8082"
#    environment:
#      - SPRING_PROFILES_ACTIVE=docker
#      - SPRING_DATASOURCE_URL=*********************************************************
#      - SPRING_DATASOURCE_USERNAME=user_user
#      - SPRING_DATASOURCE_PASSWORD=user_password
#      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
#    depends_on:
#      - user-management-db
#      eureka-server:
#        condition: service_healthy
#    networks:
#      - microservices-network
#    restart: unless-stopped

# Volumes for data persistence
volumes:
  stock_management_data:
    driver: local
  # Add more volumes for future services
  # user_management_data:
  #   driver: local

# Network for microservices communication
networks:
  microservices-network:
    driver: bridge