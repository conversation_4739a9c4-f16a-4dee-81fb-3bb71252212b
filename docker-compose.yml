version: '3.8'

services:
  stock-management-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: stock_management
      POSTGRES_USER: stock_user
      POSTGRES_PASSWORD: stock_password
    ports:
      - "5432:5432"
    volumes:
      - stock_management_data:/var/lib/postgresql/data

  eureka-server:
    build: ./eureka-server
    ports:
      - "8761:8761"

  api-gateway:
    build: ./api-gateway
    ports:
      - "8080:8080"
    environment:
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
    depends_on:
      - eureka-server

  stock-management-service:
    build: ./stockmanagementservice
    ports:
      - "8081:8081"
    environment:
      - SPRING_DATASOURCE_URL=***********************************************************
      - SPRING_DATASOURCE_USERNAME=stock_user
      - SPRING_DATASOURCE_PASSWORD=stock_password
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
    depends_on:
      - stock-management-db
      - eureka-server

volumes:
  stock_management_data: