<component name="libraryTable">
  <library name="Maven: io.micrometer:micrometer-observation:1.15.1" type="java-imported" external-system-id="Maven">
    <properties groupId="io.micrometer" artifactId="micrometer-observation" version="1.15.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>