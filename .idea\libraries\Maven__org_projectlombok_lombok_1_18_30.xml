<component name="libraryTable">
  <library name="Maven: org.projectlombok:lombok:1.18.30" type="java-imported" external-system-id="Maven">
    <properties groupId="org.projectlombok" artifactId="lombok" version="1.18.30" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30-sources.jar!/" />
    </SOURCES>
  </library>
</component>