<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-core:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-core" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-core/6.5.1/e7df1e6596b39b39f4a01000cab0318c0ed17ab0/spring-security-core-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-core/6.5.1/a0ec638f116fe08ca85bd25269f85514dd533067/spring-security-core-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>