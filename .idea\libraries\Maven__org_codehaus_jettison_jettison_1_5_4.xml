<component name="libraryTable">
  <library name="Maven: org.codehaus.jettison:jettison:1.5.4" type="java-imported" external-system-id="Maven">
    <properties groupId="org.codehaus.jettison" artifactId="jettison" version="1.5.4" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>