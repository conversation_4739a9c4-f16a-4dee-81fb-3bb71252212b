package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.StockProduit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository simple pour l'entité StockProduit - CRUD de base
 */
@Repository
public interface StockProduitRepository extends JpaRepository<StockProduit, Long> {
    
    // Recherche par stock ID
    List<StockProduit> findByStockId(Long stockId);
    
    // Recherche par produit ID
    List<StockProduit> findByProduitId(Long produitId);
    
    // Recherche par stock et produit
    List<StockProduit> findByStockIdAndProduitId(Long stockId, Long produitId);
}
