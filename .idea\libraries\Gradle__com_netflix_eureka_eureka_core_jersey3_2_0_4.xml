<component name="libraryTable">
  <library name="Gradle: com.netflix.eureka:eureka-core-jersey3:2.0.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.netflix.eureka" artifactId="eureka-core-jersey3" version="2.0.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.netflix.eureka/eureka-core-jersey3/2.0.4/83fe2bd01d0be746d6a8882da293a380628d4929/eureka-core-jersey3-2.0.4.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.netflix.eureka/eureka-core-jersey3/2.0.4/c44afe0132ec0ca7d78065c33a3785cfaae16e91/eureka-core-jersey3-2.0.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>