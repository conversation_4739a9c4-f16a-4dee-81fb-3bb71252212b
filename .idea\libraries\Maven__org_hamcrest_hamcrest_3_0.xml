<component name="libraryTable">
  <library name="Maven: org.hamcrest:hamcrest:3.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.hamcrest" artifactId="hamcrest" version="3.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/hamcrest/hamcrest/3.0/hamcrest-3.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/hamcrest/hamcrest/3.0/hamcrest-3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>