<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-security:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-security" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-security/3.5.3/7267fd219fc2508fff6f9991a5fee1aa28a28e85/spring-boot-starter-security-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-security/3.5.3/7267fd219fc2508fff6f9991a5fee1aa28a28e85/spring-boot-starter-security-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>