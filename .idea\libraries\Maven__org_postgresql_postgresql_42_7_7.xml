<component name="libraryTable">
  <library name="Maven: org.postgresql:postgresql:42.7.7" type="java-imported" external-system-id="Maven">
    <properties groupId="org.postgresql" artifactId="postgresql" version="42.7.7" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/postgresql/postgresql/42.7.7/postgresql-42.7.7.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/postgresql/postgresql/42.7.7/postgresql-42.7.7-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/postgresql/postgresql/42.7.7/postgresql-42.7.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>