<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-properties-migrator:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-properties-migrator" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-properties-migrator/3.5.3/a5ece313419e77f6d6b1fb2edcf2a28b385c4c69/spring-boot-properties-migrator-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-properties-migrator/3.5.3/4444e845177b7c18a115b882552120cda86020dd/spring-boot-properties-migrator-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>