package com.example.stocmangementservice.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO pour les stocks
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockDto {
    
    private Long id;
    
    @NotBlank(message = "Le code est obligatoire")
    @Size(max = 255, message = "Le code ne peut pas dépasser 255 caractères")
    private String code;
    
    @NotBlank(message = "Le libellé est obligatoire")
    @Size(max = 255, message = "Le libellé ne peut pas dépasser 255 caractères")
    private String libelle;
    
    @NotNull(message = "Le lieu de stockage est obligatoire")
    private Long stockageId;
    
    // Information du stockage (pour les vues)
    private String stockageLibelle;
    private String stockageCode;
    
    // Liste des produits en stock (optionnel, pour les vues détaillées)
    private List<StockProduitDto> stockProduits;
} 