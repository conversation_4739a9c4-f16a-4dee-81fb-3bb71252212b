<component name="libraryTable">
  <library name="Maven: org.apache.httpcomponents.client5:httpclient5:5.5" type="java-imported" external-system-id="Maven">
    <properties groupId="org.apache.httpcomponents.client5" artifactId="httpclient5" version="5.5" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/httpcomponents/client5/httpclient5/5.5/httpclient5-5.5.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/httpcomponents/client5/httpclient5/5.5/httpclient5-5.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/httpcomponents/client5/httpclient5/5.5/httpclient5-5.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>