<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-starter-gateway-server-webmvc:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-gateway-server-webmvc" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-starter-gateway-server-webmvc/4.3.0/e608c558ae696cf5a199854d9a87dc982c94906f/spring-cloud-starter-gateway-server-webmvc-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>