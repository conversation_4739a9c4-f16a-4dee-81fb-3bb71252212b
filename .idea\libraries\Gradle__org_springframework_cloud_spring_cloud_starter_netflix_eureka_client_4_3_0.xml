<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-netflix-eureka-client" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-starter-netflix-eureka-client/4.3.0/50528e321d9be9a89f542f62605cfad2f3b1eab8/spring-cloud-starter-netflix-eureka-client-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>