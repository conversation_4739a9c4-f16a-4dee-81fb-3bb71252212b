package com.example.stocmangementservice.controlleurs;

import com.example.stocmangementservice.model.StockJournalier;
import com.example.stocmangementservice.dtos.StockJournalierDto;
import com.example.stocmangementservice.dtos.MouvementStockRequest;
import com.example.stocmangementservice.services.MapperService;
import com.example.stocmangementservice.services.StockJournalierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/stockJournalier/")
@Tag(name = "StockJournalier", description = "API de gestion des stocks journaliers")
public class StockJournalierRestAdmin {

    private final StockJournalierService service;
    private final MapperService mapper;

    public StockJournalierRestAdmin(StockJournalierService service, MapperService mapper) {
        this.service = service;
        this.mapper = mapper;
    }

    @Operation(summary = "Finds a list of all stock journaliers")
    @GetMapping("")
    public ResponseEntity<List<StockJournalier>> findAll() {
        ResponseEntity<List<StockJournalier>> res;
        List<StockJournalier> list = service.findAll();
        HttpStatus status = HttpStatus.NO_CONTENT;
        if (list != null && !list.isEmpty())
            status = HttpStatus.OK;
        res = new ResponseEntity<>(list, status);
        return res;
    }

    @Operation(summary = "Finds an optimized list of all stock journaliers")
    @GetMapping("optimized")
    public ResponseEntity<List<StockJournalier>> findAllOptimized() {
        ResponseEntity<List<StockJournalier>> res;
        List<StockJournalier> list = service.findAll();
        HttpStatus status = HttpStatus.NO_CONTENT;
        if (list != null && !list.isEmpty())
            status = HttpStatus.OK;
        res = new ResponseEntity<>(list, status);
        return res;
    }

    @Operation(summary = "Finds a stock journalier by id")
    @GetMapping("id/{id}")
    public ResponseEntity<StockJournalier> findById(@PathVariable Long id) {
        StockJournalier t = service.findById(id);
        if (t != null) {
            return getDtoResponseEntity(t);
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Finds stock journalier by stock-produit and date")
    @GetMapping("stockProduit/{stockProduitId}/date/{date}")
    public ResponseEntity<StockJournalier> findByStockProduitAndDate(
            @PathVariable Long stockProduitId,
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        Optional<StockJournalier> t = service.findByStockProduitAndDate(stockProduitId, date);
        if (t.isPresent()) {
            return getDtoResponseEntity(t.get());
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Saves the specified stock journalier")
    @PostMapping("")
    public ResponseEntity<StockJournalier> save(@RequestBody StockJournalierDto dto) {
        if (dto == null) {
            return ResponseEntity.noContent().build();
        }
        
        StockJournalier t = service.enregistrerStock(dto);
        
        if (t == null) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } else {
            return ResponseEntity.status(HttpStatus.CREATED).body(t);
        }
    }

    @Operation(summary = "Execute stock movement")
    @PostMapping("mouvement")
    public ResponseEntity<StockJournalier> effectuerMouvement(@RequestBody MouvementStockRequest request) {
        if (request == null) {
            return ResponseEntity.noContent().build();
        }
        
        StockJournalier t = service.effectuerMouvement(request);
        return ResponseEntity.ok(t);
    }

    @Operation(summary = "Updates the specified stock journalier")
    @PutMapping("")
    public ResponseEntity<StockJournalier> update(@RequestBody StockJournalierDto dto) {
        ResponseEntity<StockJournalier> res;
        if (dto.getId() == null || service.findById(dto.getId()) == null)
            res = new ResponseEntity<>(HttpStatus.CONFLICT);
        else {
            // Update existing stock journalier
            StockJournalier updated = service.enregistrerStock(dto);
            res = new ResponseEntity<>(updated, HttpStatus.OK);
        }
        return res;
    }

    @Operation(summary = "Delete list of stock journalier")
    @PostMapping("multiple")
    public ResponseEntity<List<Long>> delete(@RequestBody List<StockJournalierDto> dtos) {
        ResponseEntity<List<Long>> res;
        HttpStatus status = HttpStatus.CONFLICT;
        List<Long> ids = new java.util.ArrayList<>();
        
        if (dtos != null && !dtos.isEmpty()) {
            for (StockJournalierDto dto : dtos) {
                if (dto.getId() != null) {
                    service.deleteById(dto.getId());
                    ids.add(dto.getId());
                }
            }
            status = HttpStatus.OK;
        }
        res = new ResponseEntity<>(ids, status);
        return res;
    }

    @Operation(summary = "Delete the specified stock journalier")
    @DeleteMapping("id/{id}")
    public ResponseEntity<Long> deleteById(@PathVariable Long id) {
        ResponseEntity<Long> res;
        HttpStatus status = HttpStatus.PRECONDITION_FAILED;
        if (id != null) {
            try {
                service.deleteById(id);
                status = HttpStatus.OK;
            } catch (Exception e) {
                status = HttpStatus.CONFLICT;
            }
        }
        res = new ResponseEntity<>(id, status);
        return res;
    }

    @Operation(summary = "Get stocks of today")
    @GetMapping("today")
    public ResponseEntity<List<StockJournalier>> getStocksDuJour() {
        List<StockJournalier> list = service.getStocksDuJour();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stocks of specific date")
    @GetMapping("date/{date}")
    public ResponseEntity<List<StockJournalier>> getStocksDuJour(
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        List<StockJournalier> list = service.getStocksDuJour(date);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stocks by date range")
    @GetMapping("periode")
    public ResponseEntity<List<StockJournalier>> findByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate dateDebut,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate dateFin) {
        List<StockJournalier> list = service.findByDateRange(dateDebut, dateFin);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stock history for stock-produit")
    @GetMapping("historique/{stockProduitId}")
    public ResponseEntity<List<StockJournalier>> getHistoriqueStock(@PathVariable Long stockProduitId) {
        List<StockJournalier> list = service.getHistoriqueStock(stockProduitId);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get latest stock for stock-produit")
    @GetMapping("latest/{stockProduitId}")
    public ResponseEntity<StockJournalier> getLatestStock(@PathVariable Long stockProduitId) {
        Optional<StockJournalier> latest = service.getLatestStock(stockProduitId);
        if (latest.isPresent()) {
            return getDtoResponseEntity(latest.get());
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Detect low stocks")
    @GetMapping("stocks-faibles")
    public ResponseEntity<List<StockJournalier>> detecterStocksFaibles(@RequestParam BigDecimal seuil) {
        List<StockJournalier> list = service.detecterStocksFaibles(seuil);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Detect stock ruptures")
    @GetMapping("ruptures")
    public ResponseEntity<List<StockJournalier>> detecterRupturesStock() {
        List<StockJournalier> list = service.detecterRupturesStock();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Search by code")
    @GetMapping("search/code/{code}")
    public ResponseEntity<List<StockJournalier>> findByCode(@PathVariable String code) {
        List<StockJournalier> list = service.findByCode(code);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Search by libelle")
    @GetMapping("search/libelle/{libelle}")
    public ResponseEntity<List<StockJournalier>> findByLibelle(@PathVariable String libelle) {
        List<StockJournalier> list = service.findByLibelle(libelle);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Filter by expedition status")
    @GetMapping("expedition/{status}")
    public ResponseEntity<List<StockJournalier>> findByExpedition(@PathVariable Boolean status) {
        List<StockJournalier> list = service.findByExpedition(status);
        HttpStatus status1 = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status1);
    }

    private ResponseEntity<StockJournalier> getDtoResponseEntity(StockJournalier stockJournalier) {
        return new ResponseEntity<>(stockJournalier, HttpStatus.OK);
    }
} 