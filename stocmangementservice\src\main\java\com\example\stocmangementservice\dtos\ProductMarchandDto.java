package com.example.stocmangementservice.dtos;

import com.example.stocmangementservice.model.TypeProduit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO pour les produits de type Marchand
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ProductMarchandDto extends ProduitDto {
    
    public ProductMarchandDto(Long id, String code, String libelle, String description, 
                             String style, Boolean defaultReclamation, Boolean export) {
        super(id, code, libelle, description, style, defaultReclamation, export, TypeProduit.MARCHAND);
    }
} 