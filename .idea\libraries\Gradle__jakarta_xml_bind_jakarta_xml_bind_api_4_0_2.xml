<component name="libraryTable">
  <library name="Gradle: jakarta.xml.bind:jakarta.xml.bind-api:4.0.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.xml.bind" artifactId="jakarta.xml.bind-api" version="4.0.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/4.0.2/6cd5a999b834b63238005b7144136379dc36cad2/jakarta.xml.bind-api-4.0.2.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/4.0.2/1e65720468e667565197d77e825ad883eb8aee1a/jakarta.xml.bind-api-4.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>