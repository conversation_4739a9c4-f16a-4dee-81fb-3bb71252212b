<component name="libraryTable">
  <library name="Maven: org.springframework:spring-beans:6.2.8" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework" artifactId="spring-beans" version="6.2.8" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>