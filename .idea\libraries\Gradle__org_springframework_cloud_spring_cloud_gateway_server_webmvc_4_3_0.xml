<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-gateway-server-webmvc:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-gateway-server-webmvc" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server-webmvc/4.3.0/de8419023e35dc9f20fc0068bbabe2729f614210/spring-cloud-gateway-server-webmvc-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server-webmvc/4.3.0/76b66b4dc481e2464a5790d912cd9cfc6f697867/spring-cloud-gateway-server-webmvc-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>