<component name="libraryTable">
  <library name="Maven: org.springframework.ws:spring-xml:4.1.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework.ws" artifactId="spring-xml" version="4.1.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/ws/spring-xml/4.1.0/spring-xml-4.1.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/ws/spring-xml/4.1.0/spring-xml-4.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/ws/spring-xml/4.1.0/spring-xml-4.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>