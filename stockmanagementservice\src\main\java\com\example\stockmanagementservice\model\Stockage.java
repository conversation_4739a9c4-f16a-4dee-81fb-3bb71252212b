package com.example.stockmanagementservice.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Entity
@Table(name = "stockage")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Stockage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String libelle;
    
    @Column(nullable = false, unique = true)
    private String code;
    
    @OneToMany(mappedBy = "stockage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Stock> stocks;
} 
