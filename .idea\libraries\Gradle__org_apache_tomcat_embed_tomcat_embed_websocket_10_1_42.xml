<component name="libraryTable">
  <library name="Gradle: org.apache.tomcat.embed:tomcat-embed-websocket:10.1.42" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.apache.tomcat.embed" artifactId="tomcat-embed-websocket" version="10.1.42" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-websocket/10.1.42/e47e420583a372feaece0dc9b8a9a63c4bfcd093/tomcat-embed-websocket-10.1.42.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-websocket/10.1.42/17de9506e56428ee326eab6089905201ae3e8eae/tomcat-embed-websocket-10.1.42-sources.jar!/" />
    </SOURCES>
  </library>
</component>