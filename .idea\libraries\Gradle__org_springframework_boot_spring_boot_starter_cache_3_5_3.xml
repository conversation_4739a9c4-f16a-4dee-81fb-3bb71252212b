<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-cache:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-cache" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-cache/3.5.3/405bae1b563affb0e5a847db0cc4829d1f513891/spring-boot-starter-cache-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-cache/3.5.3/405bae1b563affb0e5a847db0cc4829d1f513891/spring-boot-starter-cache-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>