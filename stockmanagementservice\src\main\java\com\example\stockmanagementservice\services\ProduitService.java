package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.CreateProduitRequest;
import com.example.stockmanagementservice.dtos.ProduitDto;
import com.example.stockmanagementservice.exceptions.DuplicateCodeException;
import com.example.stockmanagementservice.exceptions.ProduitNotFoundException;
import com.example.stockmanagementservice.model.ProductMarchand;
import com.example.stockmanagementservice.model.ProductSource;
import com.example.stockmanagementservice.model.Produit;
import com.example.stockmanagementservice.model.TypeProduit;
import com.example.stockmanagementservice.repositories.ProduitRepository;
import com.example.stockmanagementservice.repositories.ProductMarchandRepository;
import com.example.stockmanagementservice.repositories.ProductSourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service principal pour la gestion des produits (Marchand et Source)
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProduitService {
    
    private final ProduitRepository produitRepository;
    private final ProductMarchandRepository productMarchandRepository;
    private final ProductSourceRepository productSourceRepository;
    
    /**
     * Créer un nouveau produit basé sur le type
     */
    public Produit createProduit(CreateProduitRequest request) {
        log.info("Création d'un nouveau produit : {}", request.getCode());
        
        // Vérifier l'unicité du code
        if (produitRepository.existsByCode(request.getCode())) {
            throw new DuplicateCodeException("Produit", request.getCode());
        }
        
        Produit produit;
        
        switch (request.getTypeProduit()) {
            case MARCHAND -> {
                produit = new ProductMarchand();
                setCommonFields(produit, request);
                produit = productMarchandRepository.save((ProductMarchand) produit);
            }
            case SOURCE -> {
                produit = new ProductSource();
                setCommonFields(produit, request);
                produit = productSourceRepository.save((ProductSource) produit);
            }
            default -> throw new IllegalArgumentException("Type de produit non supporté: " + request.getTypeProduit());
        }
        
        log.info("Produit créé avec succès : ID={}, Code={}", produit.getId(), produit.getCode());
        return produit;
    }
    
    /**
     * Rechercher un produit par ID
     */
    @Transactional(readOnly = true)
    public Produit findById(Long id) {
        return produitRepository.findById(id)
                .orElseThrow(() -> new ProduitNotFoundException(id));
    }
    
    /**
     * Rechercher un produit par code
     */
    @Transactional(readOnly = true)
    public Produit findByCode(String code) {
        return produitRepository.findByCode(code)
                .orElseThrow(() -> new ProduitNotFoundException(code));
    }
    
    /**
     * Rechercher tous les produits avec pagination
     */
    @Transactional(readOnly = true)
    public Page<Produit> findAll(Pageable pageable) {
        return produitRepository.findAll(pageable);
    }
    
    /**
     * Rechercher les produits par type
     */
    @Transactional(readOnly = true)
    public List<Produit> findByType(Class<? extends Produit> type) {
        return produitRepository.findByType(type);
    }
    
    /**
     * Rechercher les produits marchands
     */
    @Transactional(readOnly = true)
    public List<ProductMarchand> findAllProductMarchand() {
        return productMarchandRepository.findAll();
    }
    
    /**
     * Rechercher les produits sources
     */
    @Transactional(readOnly = true)
    public List<ProductSource> findAllProductSource() {
        return productSourceRepository.findAll();
    }
    
    /**
     * Rechercher par libellé (contient)
     */
    @Transactional(readOnly = true)
    public List<Produit> findByLibelleContaining(String libelle) {
        return produitRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    /**
     * Recherche multicritères avec pagination
     */
    @Transactional(readOnly = true)
    public Page<Produit> searchProduits(String code, String libelle, 
                                       Class<? extends Produit> typeProduit, 
                                       String style, Pageable pageable) {
        return produitRepository.findBySearchCriteria(code, libelle, typeProduit, style, pageable);
    }
    
    /**
     * Mettre à jour un produit
     */
    public Produit updateProduit(Long id, CreateProduitRequest request) {
        log.info("Mise à jour du produit ID={}", id);
        
        Produit produit = findById(id);
        
        // Vérifier l'unicité du code si changé
        if (!produit.getCode().equals(request.getCode()) && 
            produitRepository.existsByCode(request.getCode())) {
            throw new DuplicateCodeException("Produit", request.getCode());
        }
        
        setCommonFields(produit, request);
        produit = produitRepository.save(produit);
        
        log.info("Produit mis à jour avec succès : ID={}", id);
        return produit;
    }
    
    /**
     * Supprimer un produit
     */
    public void deleteProduit(Long id) {
        log.info("Suppression du produit ID={}", id);
        
        Produit produit = findById(id);
        
        // Vérifier s'il n'y a pas de stocks associés
        if (!produit.getStockProduits().isEmpty()) {
            throw new IllegalStateException("Impossible de supprimer un produit ayant des stocks associés");
        }
        
        produitRepository.deleteById(id);
        log.info("Produit supprimé avec succès : ID={}", id);
    }
    
    /**
     * Rechercher les produits ayant des stocks
     */
    @Transactional(readOnly = true)
    public List<Produit> findProduitsWithStock() {
        return produitRepository.findProduitsWithStock();
    }
    
    /**
     * Rechercher les produits sans stocks
     */
    @Transactional(readOnly = true)
    public List<Produit> findProduitsSansStock() {
        return produitRepository.findProduitsSansStock();
    }
    
    /**
     * Rechercher les produits exportables
     */
    @Transactional(readOnly = true)
    public List<Produit> findProduitsExportables() {
        return produitRepository.findByExportTrue();
    }
    
    /**
     * Rechercher les produits avec réclamation par défaut
     */
    @Transactional(readOnly = true)
    public List<Produit> findProduitsAvecReclamation() {
        return produitRepository.findByDefaultReclamationTrue();
    }
    
    /**
     * Compter les produits par type
     */
    @Transactional(readOnly = true)
    public Long countByType(Class<? extends Produit> type) {
        return produitRepository.countByType(type);
    }
    
    /**
     * Vérifier l'existence d'un produit par code
     */
    @Transactional(readOnly = true)
    public boolean existsByCode(String code) {
        return produitRepository.existsByCode(code);
    }
    
    /**
     * Activer/Désactiver l'export pour un produit
     */
    public Produit toggleExport(Long id) {
        Produit produit = findById(id);
        produit.setExport(!produit.getExport());
        return produitRepository.save(produit);
    }
    
    /**
     * Activer/Désactiver la réclamation par défaut
     */
    public Produit toggleDefaultReclamation(Long id) {
        Produit produit = findById(id);
        produit.setDefaultReclamation(!produit.getDefaultReclamation());
        return produitRepository.save(produit);
    }
    
    /**
     * Méthode utilitaire pour définir les champs communs
     */
    private void setCommonFields(Produit produit, CreateProduitRequest request) {
        produit.setCode(request.getCode());
        produit.setLibelle(request.getLibelle());
        produit.setDescription(request.getDescription());
        produit.setStyle(request.getStyle());
        produit.setDefaultReclamation(request.getDefaultReclamation());
        produit.setExport(request.getExport());
    }
} 
