package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.ProduitDto;
import com.example.stockmanagementservice.model.Produit;
import com.example.stockmanagementservice.model.ProductMarchand;
import com.example.stockmanagementservice.repositories.ProduitRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des produits - CRUD de base
 */
@Service
public class ProduitService {

    @Autowired
    private ProduitRepository produitRepository;

    // Créer un produit
    public Produit createProduit(Produit produit) {
        return produitRepository.save(produit);
    }

    // Récupérer tous les produits
    public List<Produit> getAllProduits() {
        return produitRepository.findAll();
    }

    // Récupérer un produit par ID
    public Optional<Produit> getProduitById(Long id) {
        return produitRepository.findById(id);
    }

    // Récupérer un produit par code
    public Optional<Produit> getProduitByCode(String code) {
        return produitRepository.findByCode(code);
    }

    // Rechercher par libellé
    public List<Produit> searchByLibelle(String libelle) {
        return produitRepository.findByLibelleContainingIgnoreCase(libelle);
    }

    // Mettre à jour un produit
    public Produit updateProduit(Long id, Produit produitDetails) {
        Optional<Produit> optionalProduit = produitRepository.findById(id);
        if (optionalProduit.isPresent()) {
            Produit produit = optionalProduit.get();
            produit.setCode(produitDetails.getCode());
            produit.setLibelle(produitDetails.getLibelle());
            produit.setDescription(produitDetails.getDescription());
            return produitRepository.save(produit);
        }
        return null;
    }

    // Supprimer un produit
    public boolean deleteProduit(Long id) {
        if (produitRepository.existsById(id)) {
            produitRepository.deleteById(id);
            return true;
        }
        return false;
    }

    // Convertir entité vers DTO
    public ProduitDto convertToDto(Produit produit) {
        return new ProduitDto(
            produit.getId(),
            produit.getCode(),
            produit.getLibelle(),
            produit.getDescription()
        );
    }

    // Convertir DTO vers entité (Produit est abstract, utiliser ProductMarchand par défaut)
    public Produit convertToEntity(ProduitDto dto) {
        // Comme Produit est abstract, on crée un ProductMarchand par défaut
        ProductMarchand produit = new ProductMarchand();
        produit.setId(dto.getId());
        produit.setCode(dto.getCode());
        produit.setLibelle(dto.getLibelle());
        produit.setDescription(dto.getDescription());
        return produit;
    }
} 
