package com.example.stocmangementservice.controlleurs;

import com.example.stocmangementservice.model.StockProduit;
import com.example.stocmangementservice.dtos.StockProduitDto;
import com.example.stocmangementservice.services.MapperService;
import com.example.stocmangementservice.services.StockProduitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/stockProduit/")
@Tag(name = "StockProduit", description = "API de gestion des relations stock-produit")
public class StockProduitRestAdmin {

    private final StockProduitService service;
    private final MapperService mapper;

    public StockProduitRestAdmin(StockProduitService service, MapperService mapper) {
        this.service = service;
        this.mapper = mapper;
    }

    @Operation(summary = "Finds a list of all stock-produit relations")
    @GetMapping("")
    public ResponseEntity<List<StockProduit>> findAll() {
        ResponseEntity<List<StockProduit>> res;
        List<StockProduit> list = service.findAll();
        HttpStatus status = HttpStatus.NO_CONTENT;
        if (list != null && !list.isEmpty())
            status = HttpStatus.OK;
        res = new ResponseEntity<>(list, status);
        return res;
    }

    @Operation(summary = "Finds an optimized list of all stock-produit relations")
    @GetMapping("optimized")
    public ResponseEntity<List<StockProduit>> findAllOptimized() {
        ResponseEntity<List<StockProduit>> res;
        List<StockProduit> list = service.findAll();
        HttpStatus status = HttpStatus.NO_CONTENT;
        if (list != null && !list.isEmpty())
            status = HttpStatus.OK;
        res = new ResponseEntity<>(list, status);
        return res;
    }

    @Operation(summary = "Finds a stock-produit relation by id")
    @GetMapping("id/{id}")
    public ResponseEntity<StockProduit> findById(@PathVariable Long id) {
        StockProduit t = service.findById(id);
        if (t != null) {
            return getDtoResponseEntity(t);
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Finds a stock-produit relation by stock and produit ids")
    @GetMapping("stock/{stockId}/produit/{produitId}")
    public ResponseEntity<StockProduit> findByStockAndProduit(
            @PathVariable Long stockId, 
            @PathVariable Long produitId) {
        Optional<StockProduit> t = service.findByStockIdAndProduitId(stockId, produitId);
        if (t.isPresent()) {
            return getDtoResponseEntity(t.get());
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Saves the specified stock-produit relation")
    @PostMapping("")
    public ResponseEntity<StockProduit> save(@RequestBody StockProduitDto dto) {
        if (dto == null) {
            return ResponseEntity.noContent().build();
        }
        
        StockProduit t = service.createStockProduit(dto);
        
        if (t == null) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } else {
            return ResponseEntity.status(HttpStatus.CREATED).body(t);
        }
    }

    @Operation(summary = "Updates the specified stock-produit relation")
    @PutMapping("")
    public ResponseEntity<StockProduit> update(@RequestBody StockProduitDto dto) {
        ResponseEntity<StockProduit> res;
        if (dto == null || dto.getStockId() == null || dto.getProduitId() == null)
            res = new ResponseEntity<>(HttpStatus.CONFLICT);
        else {
            // Note: StockProduit might not have an update method, only create/delete
            res = new ResponseEntity<>(HttpStatus.METHOD_NOT_ALLOWED);
        }
        return res;
    }

    @Operation(summary = "Delete list of stock-produit relations")
    @PostMapping("multiple")
    public ResponseEntity<List<String>> delete(@RequestBody List<StockProduitDto> dtos) {
        ResponseEntity<List<String>> res;
        HttpStatus status = HttpStatus.CONFLICT;
        List<String> deletedRelations = new java.util.ArrayList<>();
        
        if (dtos != null && !dtos.isEmpty()) {
            for (StockProduitDto dto : dtos) {
                if (dto.getStockId() != null && dto.getProduitId() != null) {
                    service.deleteByStockIdAndProduitId(dto.getStockId(), dto.getProduitId());
                    deletedRelations.add("Stock:" + dto.getStockId() + "-Produit:" + dto.getProduitId());
                }
            }
            status = HttpStatus.OK;
        }
        res = new ResponseEntity<>(deletedRelations, status);
        return res;
    }

    @Operation(summary = "Delete the specified stock-produit relation")
    @DeleteMapping("id/{id}")
    public ResponseEntity<Long> deleteById(@PathVariable Long id) {
        ResponseEntity<Long> res;
        HttpStatus status = HttpStatus.PRECONDITION_FAILED;
        if (id != null) {
            try {
                service.deleteStockProduit(id);
                status = HttpStatus.OK;
            } catch (Exception e) {
                status = HttpStatus.CONFLICT;
            }
        }
        res = new ResponseEntity<>(id, status);
        return res;
    }

    @Operation(summary = "Delete by stock and produit ids")
    @DeleteMapping("stock/{stockId}/produit/{produitId}")
    public ResponseEntity<String> deleteByStockAndProduit(
            @PathVariable Long stockId,
            @PathVariable Long produitId) {
        try {
            service.deleteByStockIdAndProduitId(stockId, produitId);
            return ResponseEntity.ok("Relation supprimée avec succès");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body("Erreur lors de la suppression");
        }
    }

    @Operation(summary = "Get stock-produit relations by stock")
    @GetMapping("stock/{stockId}")
    public ResponseEntity<List<StockProduit>> getByStock(@PathVariable Long stockId) {
        List<StockProduit> list = service.findByStockId(stockId);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stock-produit relations by produit")
    @GetMapping("produit/{produitId}")
    public ResponseEntity<List<StockProduit>> getByProduit(@PathVariable Long produitId) {
        List<StockProduit> list = service.findByProduitId(produitId);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stock-produit relations by stockage")
    @GetMapping("stockage/{stockageId}")
    public ResponseEntity<List<StockProduit>> getByStockage(@PathVariable Long stockageId) {
        List<StockProduit> list = service.findByStockageId(stockageId);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stock-produit relations with journaliers")
    @GetMapping("with-journaliers")
    public ResponseEntity<List<StockProduit>> getWithJournaliers() {
        List<StockProduit> list = service.findStockProduitsWithJournaliers();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stock-produit relations without journaliers")
    @GetMapping("without-journaliers")
    public ResponseEntity<List<StockProduit>> getWithoutJournaliers() {
        List<StockProduit> list = service.findStockProduitsSansSuivi();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Search by stock code")
    @GetMapping("search/stock-code/{code}")
    public ResponseEntity<List<StockProduit>> findByStockCode(@PathVariable String code) {
        List<StockProduit> list = service.findByStockCode(code);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Search by produit code")
    @GetMapping("search/produit-code/{code}")
    public ResponseEntity<List<StockProduit>> findByProduitCode(@PathVariable String code) {
        List<StockProduit> list = service.findByProduitCode(code);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Check if relation exists")
    @GetMapping("exists/stock/{stockId}/produit/{produitId}")
    public ResponseEntity<Boolean> existsByStockAndProduit(
            @PathVariable Long stockId,
            @PathVariable Long produitId) {
        boolean exists = service.existsByStockIdAndProduitId(stockId, produitId);
        return ResponseEntity.ok(exists);
    }

    @Operation(summary = "Count relations")
    @GetMapping("count")
    public ResponseEntity<Long> count() {
        long count = service.count();
        return ResponseEntity.ok(count);
    }

    @Operation(summary = "Count journaliers by stock-produit")
    @GetMapping("count-journaliers")
    public ResponseEntity<List<Object[]>> countJournaliersByStockProduit() {
        List<Object[]> list = service.countJournaliersByStockProduit();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    private ResponseEntity<StockProduit> getDtoResponseEntity(StockProduit stockProduit) {
        return new ResponseEntity<>(stockProduit, HttpStatus.OK);
    }
} 