package com.example.stocmangementservice.exceptions;

import java.math.BigDecimal;

/**
 * Exception levée quand la quantité en stock est insuffisante
 */
public class StockInsufficientException extends StockException {
    
    public StockInsufficientException(String produitCode, BigDecimal quantiteDemandee, BigDecimal quantiteDisponible) {
        super(String.format("Stock insuffisant pour le produit %s. Quantité demandée: %s, Quantité disponible: %s", 
              produitCode, quantiteDemandee.toString(), quantiteDisponible.toString()));
    }
    
    public StockInsufficientException(String message) {
        super(message);
    }
    
    public StockInsufficientException(String message, Throwable cause) {
        super(message, cause);
    }
} 