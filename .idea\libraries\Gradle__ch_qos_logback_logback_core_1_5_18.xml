<component name="libraryTable">
  <library name="Gradle: ch.qos.logback:logback-core:1.5.18" type="java-imported" external-system-id="GRADLE">
    <properties groupId="ch.qos.logback" artifactId="logback-core" version="1.5.18" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.5.18/6c0375624f6f36b4e089e2488ba21334a11ef13f/logback-core-1.5.18.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.5.18/f0838c78e8957d0ea1a6b55a2df250bf2d559d9c/logback-core-1.5.18-sources.jar!/" />
    </SOURCES>
  </library>
</component>