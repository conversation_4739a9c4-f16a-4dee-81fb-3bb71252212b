<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-netflix-eureka-client:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-netflix-eureka-client" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-netflix-eureka-client/4.3.0/5fd7476e8b9c4842bf44e2abad06ad9e4a50cbe7/spring-cloud-netflix-eureka-client-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-netflix-eureka-client/4.3.0/45c5624dbb753ab260219e18ab0fc8666379eed0/spring-cloud-netflix-eureka-client-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>