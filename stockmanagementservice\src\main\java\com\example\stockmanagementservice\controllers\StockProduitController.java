package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.StockProduitDto;
import com.example.stockmanagementservice.model.StockProduit;
import com.example.stockmanagementservice.services.StockProduitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des relations stock-produit
 */
@RestController
@RequestMapping("/api/stock-produits")
public class StockProduitController {
    
    @Autowired
    private StockProduitService stockProduitService;
    
    // Créer une relation stock-produit
    @PostMapping
    public ResponseEntity<StockProduitDto> createStockProduit(@RequestBody StockProduitDto stockProduitDto) {
        try {
            StockProduit stockProduit = stockProduitService.convertToEntity(stockProduitDto);
            StockProduit savedStockProduit = stockProduitService.createStockProduit(stockProduit);
            StockProduitDto responseDto = stockProduitService.convertToDto(savedStockProduit);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer toutes les relations stock-produit
    @GetMapping
    public ResponseEntity<List<StockProduitDto>> getAllStockProduits() {
        try {
            List<StockProduit> stockProduits = stockProduitService.getAllStockProduits();
            List<StockProduitDto> stockProduitsDto = stockProduits.stream()
                .map(stockProduitService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockProduitsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer une relation par ID
    @GetMapping("/{id}")
    public ResponseEntity<StockProduitDto> getStockProduitById(@PathVariable Long id) {
        try {
            Optional<StockProduit> stockProduit = stockProduitService.getStockProduitById(id);
            if (stockProduit.isPresent()) {
                StockProduitDto stockProduitDto = stockProduitService.convertToDto(stockProduit.get());
                return new ResponseEntity<>(stockProduitDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les produits d'un stock
    @GetMapping("/stock/{stockId}")
    public ResponseEntity<List<StockProduitDto>> getStockProduitsByStock(@PathVariable Long stockId) {
        try {
            List<StockProduit> stockProduits = stockProduitService.getStockProduitsByStockId(stockId);
            List<StockProduitDto> stockProduitsDto = stockProduits.stream()
                .map(stockProduitService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockProduitsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks d'un produit
    @GetMapping("/produit/{produitId}")
    public ResponseEntity<List<StockProduitDto>> getStockProduitsByProduit(@PathVariable Long produitId) {
        try {
            List<StockProduit> stockProduits = stockProduitService.getStockProduitsByProduitId(produitId);
            List<StockProduitDto> stockProduitsDto = stockProduits.stream()
                .map(stockProduitService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockProduitsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer une relation spécifique stock-produit
    @GetMapping("/stock/{stockId}/produit/{produitId}")
    public ResponseEntity<List<StockProduitDto>> getStockProduitsByStockAndProduit(
            @PathVariable Long stockId, @PathVariable Long produitId) {
        try {
            List<StockProduit> stockProduits = stockProduitService.getStockProduitsByStockAndProduit(stockId, produitId);
            List<StockProduitDto> stockProduitsDto = stockProduits.stream()
                .map(stockProduitService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stockProduitsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour une relation stock-produit
    @PutMapping("/{id}")
    public ResponseEntity<StockProduitDto> updateStockProduit(@PathVariable Long id, @RequestBody StockProduitDto stockProduitDto) {
        try {
            StockProduit stockProduitDetails = stockProduitService.convertToEntity(stockProduitDto);
            StockProduit updatedStockProduit = stockProduitService.updateStockProduit(id, stockProduitDetails);
            if (updatedStockProduit != null) {
                StockProduitDto responseDto = stockProduitService.convertToDto(updatedStockProduit);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer une relation stock-produit
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteStockProduit(@PathVariable Long id) {
        try {
            boolean deleted = stockProduitService.deleteStockProduit(id);
            if (deleted) {
                return new ResponseEntity<>("Relation stock-produit supprimée avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Relation stock-produit non trouvée", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
