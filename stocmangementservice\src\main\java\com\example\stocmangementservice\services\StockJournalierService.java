package com.example.stocmangementservice.services;

import com.example.stocmangementservice.dtos.MouvementStockRequest;
import com.example.stocmangementservice.dtos.StockJournalierDto;
import com.example.stocmangementservice.exceptions.InvalidOperationException;
import com.example.stocmangementservice.exceptions.StockInsufficientException;
import com.example.stocmangementservice.exceptions.StockNotFoundException;
import com.example.stocmangementservice.model.*;
import com.example.stocmangementservice.repositories.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des stocks journaliers
 */
@Service
@RequiredArgsConstructor
@Transactional
public class StockJournalierService {
    
    private static final Logger log = LoggerFactory.getLogger(StockJournalierService.class);
    
    private final StockJournalierRepository stockJournalierRepository;
    private final StockProduitRepository stockProduitRepository;
    private final StockageRepository stockageRepository;
    private final StockRepository stockRepository;
    private final ProduitRepository produitRepository;
    
    /**
     * Enregistrer un stock journalier
     */
    public StockJournalier enregistrerStock(StockJournalierDto stockJournalierDto) {
        log.info("Enregistrement du stock journalier pour le {}", stockJournalierDto.getJour());
        
        StockProduit stockProduit = stockProduitRepository.findById(stockJournalierDto.getStockProduitId())
            .orElseThrow(() -> new StockNotFoundException("StockProduit non trouvé avec l'ID: " + stockJournalierDto.getStockProduitId()));
        
        // Vérifier l'unicité de la date pour ce stock-produit
        if (stockJournalierRepository.existsByStockProduitAndJour(stockProduit, stockJournalierDto.getJour())) {
            StockJournalier existing = stockJournalierRepository.findByStockProduitAndJour(
                    stockProduit, stockJournalierDto.getJour()).get();
            updateStockJournalierFromDto(existing, stockJournalierDto);
            return stockJournalierRepository.save(existing);
        }
        
        StockJournalier stockJournalier = new StockJournalier();
        stockJournalier.setJour(stockJournalierDto.getJour());
        stockJournalier.setCode(stockJournalierDto.getCode());
        stockJournalier.setLibelle(stockJournalierDto.getLibelle());
        stockJournalier.setDescription(stockJournalierDto.getDescription());
        stockJournalier.setQuantite(stockJournalierDto.getQuantite() != null ? stockJournalierDto.getQuantite() : BigDecimal.ZERO);
        stockJournalier.setExpedition(stockJournalierDto.getExpedition());
        stockJournalier.setStockProduit(stockProduit);
        
        // Définir les relations supplémentaires si disponibles
        if (stockProduit.getStock() != null) {
            stockJournalier.setStock(stockProduit.getStock());
            if (stockProduit.getStock().getStockage() != null) {
                stockJournalier.setStockage(stockProduit.getStock().getStockage());
            }
        }
        if (stockProduit.getProduit() != null) {
            stockJournalier.setProduit(stockProduit.getProduit());
        }
        
        return stockJournalierRepository.save(stockJournalier);
    }
    
    /**
     * Effectuer un mouvement de stock
     */
    public StockJournalier effectuerMouvement(MouvementStockRequest request) {
        log.info("Mouvement de stock : {} {} le {}", request.getTypeOperation(), request.getQuantite(), request.getDate());
        
        StockProduit stockProduit = stockProduitRepository.findById(request.getStockProduitId())
            .orElseThrow(() -> new StockNotFoundException("StockProduit non trouvé"));
        
        Optional<StockJournalier> stockActuel = stockJournalierRepository
                .findByStockProduitAndJour(stockProduit, request.getDate());
        
        BigDecimal nouvelleQuantite;
        if (stockActuel.isPresent()) {
            BigDecimal quantiteActuelle = stockActuel.get().getQuantite();
            
            switch (request.getTypeOperation()) {
                case ENTREE -> nouvelleQuantite = quantiteActuelle.add(request.getQuantite());
                case SORTIE -> {
                    if (quantiteActuelle.compareTo(request.getQuantite()) < 0) {
                        throw new StockInsufficientException(
                                stockProduit.getProduit().getCode(),
                                request.getQuantite(),
                                quantiteActuelle
                        );
                    }
                    nouvelleQuantite = quantiteActuelle.subtract(request.getQuantite());
                }
                case AJUSTEMENT -> nouvelleQuantite = request.getQuantite();
                default -> throw new InvalidOperationException("Type d'opération non supporté");
            }
            
            stockActuel.get().setQuantite(nouvelleQuantite);
            return stockJournalierRepository.save(stockActuel.get());
        } else {
            if (request.getTypeOperation() == MouvementStockRequest.TypeOperation.SORTIE) {
                throw new StockInsufficientException(
                        stockProduit.getProduit().getCode(),
                        request.getQuantite(),
                        BigDecimal.ZERO
                );
            }
            
            StockJournalier nouveau = new StockJournalier();
            nouveau.setJour(request.getDate());
            nouveau.setQuantite(request.getQuantite());
            nouveau.setStockProduit(stockProduit);
            
            // Définir les relations supplémentaires
            if (stockProduit.getStock() != null) {
                nouveau.setStock(stockProduit.getStock());
                if (stockProduit.getStock().getStockage() != null) {
                    nouveau.setStockage(stockProduit.getStock().getStockage());
                }
            }
            if (stockProduit.getProduit() != null) {
                nouveau.setProduit(stockProduit.getProduit());
            }
            
            return stockJournalierRepository.save(nouveau);
        }
    }
    
    /**
     * Récupérer un stock journalier par ID
     */
    @Transactional(readOnly = true)
    public StockJournalier findById(Long id) {
        return stockJournalierRepository.findById(id)
                .orElseThrow(() -> new StockNotFoundException("Stock journalier non trouvé avec l'ID: " + id));
    }
    
    /**
     * Rechercher par stock-produit et date
     */
    @Transactional(readOnly = true)
    public Optional<StockJournalier> findByStockProduitAndDate(Long stockProduitId, LocalDate date) {
        StockProduit stockProduit = stockProduitRepository.findById(stockProduitId)
            .orElseThrow(() -> new StockNotFoundException("StockProduit non trouvé avec l'ID: " + stockProduitId));
        return stockJournalierRepository.findByStockProduitAndJour(stockProduit, date);
    }
    
    /**
     * Rechercher tous les stocks journaliers
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> findAll() {
        return stockJournalierRepository.findAll();
    }
    
    /**
     * Rechercher par plage de dates
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> findByDateRange(LocalDate dateDebut, LocalDate dateFin) {
        return stockJournalierRepository.findByJourBetween(dateDebut, dateFin);
    }
    
    /**
     * Rechercher les stocks du jour
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> getStocksDuJour(LocalDate date) {
        return stockJournalierRepository.findStocksDuJour(date);
    }
    
    /**
     * Rechercher les stocks d'aujourd'hui
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> getStocksDuJour() {
        return getStocksDuJour(LocalDate.now());
    }
    
    /**
     * Détecter les stocks faibles selon un seuil
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> detecterStocksFaibles(BigDecimal seuil) {
        return stockJournalierRepository.findStocksFaibles(seuil);
    }
    
    /**
     * Détecter les ruptures de stock
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> detecterRupturesStock() {
        return stockJournalierRepository.findRupturesStock();
    }
    
    /**
     * Recherche multicritères avec pagination
     */
    @Transactional(readOnly = true)
    public Page<StockJournalier> findBySearchCriteria(
            Long stockProduitId,
            LocalDate dateDebut,
            LocalDate dateFin,
            BigDecimal quantiteMin,
            BigDecimal quantiteMax,
            Pageable pageable) {
        return stockJournalierRepository.findBySearchCriteria(
                stockProduitId, dateDebut, dateFin, quantiteMin, quantiteMax, pageable);
    }
    
    /**
     * Obtenir l'historique d'un stock-produit
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> getHistoriqueStock(Long stockProduitId) {
        StockProduit stockProduit = stockProduitRepository.findById(stockProduitId)
            .orElseThrow(() -> new StockNotFoundException("StockProduit non trouvé avec l'ID: " + stockProduitId));
        return stockJournalierRepository.findByStockProduitOrderByJourDesc(stockProduit);
    }
    
    /**
     * Obtenir le dernier stock journalier pour un stock-produit
     */
    @Transactional(readOnly = true)
    public Optional<StockJournalier> getLatestStock(Long stockProduitId) {
        StockProduit stockProduit = stockProduitRepository.findById(stockProduitId)
            .orElseThrow(() -> new StockNotFoundException("StockProduit non trouvé avec l'ID: " + stockProduitId));
        return stockJournalierRepository.findLatestByStockProduit(stockProduit);
    }
    
    /**
     * Rechercher par code
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> findByCode(String code) {
        return stockJournalierRepository.findByCodeContainingIgnoreCase(code);
    }
    
    /**
     * Rechercher par libellé
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> findByLibelle(String libelle) {
        return stockJournalierRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    /**
     * Rechercher par expédition
     */
    @Transactional(readOnly = true)
    public List<StockJournalier> findByExpedition(Boolean expedition) {
        return stockJournalierRepository.findByExpedition(expedition);
    }
    
    /**
     * Supprimer un stock journalier
     */
    public void deleteById(Long id) {
        if (!stockJournalierRepository.existsById(id)) {
            throw new StockNotFoundException("Stock journalier non trouvé avec l'ID: " + id);
        }
        stockJournalierRepository.deleteById(id);
        log.info("Stock journalier supprimé : ID={}", id);
    }
    
    /**
     * Méthode utilitaire pour mettre à jour une entité à partir d'un DTO
     */
    private void updateStockJournalierFromDto(StockJournalier stockJournalier, StockJournalierDto dto) {
        if (dto.getCode() != null) stockJournalier.setCode(dto.getCode());
        if (dto.getLibelle() != null) stockJournalier.setLibelle(dto.getLibelle());
        if (dto.getDescription() != null) stockJournalier.setDescription(dto.getDescription());
        if (dto.getQuantite() != null) stockJournalier.setQuantite(dto.getQuantite());
        if (dto.getExpedition() != null) stockJournalier.setExpedition(dto.getExpedition());
    }
} 