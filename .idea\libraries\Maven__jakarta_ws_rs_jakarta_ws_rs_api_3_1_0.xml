<component name="libraryTable">
  <library name="Maven: jakarta.ws.rs:jakarta.ws.rs-api:3.1.0" type="java-imported" external-system-id="Maven">
    <properties groupId="jakarta.ws.rs" artifactId="jakarta.ws.rs-api" version="3.1.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>