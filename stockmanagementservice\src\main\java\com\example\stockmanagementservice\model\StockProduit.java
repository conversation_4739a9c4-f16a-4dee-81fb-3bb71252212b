package com.example.stockmanagementservice.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Entity
@Table(name = "stock_produit", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"stock_id", "produit_id"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockProduit {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stock_id", nullable = false)
    private Stock stock;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id", nullable = false)
    private Produit produit;
    
    @OneToMany(mappedBy = "stockProduit", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StockJournalier> stockJournaliers;
} 
