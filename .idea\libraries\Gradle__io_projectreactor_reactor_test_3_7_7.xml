<component name="libraryTable">
  <library name="Gradle: io.projectreactor:reactor-test:3.7.7" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.projectreactor" artifactId="reactor-test" version="3.7.7" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.projectreactor/reactor-test/3.7.7/aade0f4225dc391f12ddfe195ba6691e61812f92/reactor-test-3.7.7.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.projectreactor/reactor-test/3.7.7/deaa5359e75c67424ecfe3b55d62d78c726d4d80/reactor-test-3.7.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>