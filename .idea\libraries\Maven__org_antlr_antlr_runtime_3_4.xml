<component name="libraryTable">
  <library name="Maven: org.antlr:antlr-runtime:3.4" type="java-imported" external-system-id="Maven">
    <properties groupId="org.antlr" artifactId="antlr-runtime" version="3.4" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>