<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-autoconfigure:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-autoconfigure" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/3.5.3/b03483a9d3c480d064c8380e2d78ea3593ca6cb8/spring-boot-autoconfigure-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/3.5.3/e54e2bc6372bf03edf52802c1c11ecb02019df01/spring-boot-autoconfigure-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>