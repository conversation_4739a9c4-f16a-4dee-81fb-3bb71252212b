<component name="libraryTable">
  <library name="Maven: com.jayway.jsonpath:json-path:2.9.0" type="java-imported" external-system-id="Maven">
    <properties groupId="com.jayway.jsonpath" artifactId="json-path" version="2.9.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>