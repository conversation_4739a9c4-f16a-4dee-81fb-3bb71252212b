<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-jdbc:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-jdbc" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-jdbc/3.5.3/427ff2d6cc12a5e503b1be8508db08f0c05098c0/spring-boot-starter-jdbc-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-jdbc/3.5.3/1f3afa20fdf5c2357fd6cbaeaddfe9d4e10e9ac5/spring-boot-starter-jdbc-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>