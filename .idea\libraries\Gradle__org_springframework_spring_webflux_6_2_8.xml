<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-webflux:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-webflux" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webflux/6.2.8/6a7a83751694719b8bf6601f76b37c91651f1e1e/spring-webflux-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-webflux/6.2.8/e634bf036ec7b93e81061c28d6daef38460b0a33/spring-webflux-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>