package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.Stock;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository simple pour l'entité Stock - CRUD de base
 */
@Repository
public interface StockRepository extends JpaRepository<Stock, Long> {

    // Recherche par code
    Optional<Stock> findByCode(String code);

    // Recherche par libellé
    List<Stock> findByLibelleContainingIgnoreCase(String libelle);

    // Vérifier l'existence par code
    boolean existsByCode(String code);

    // Stocks ordonnés par libellé
    List<Stock> findAllByOrderByLibelleAsc();
} 
