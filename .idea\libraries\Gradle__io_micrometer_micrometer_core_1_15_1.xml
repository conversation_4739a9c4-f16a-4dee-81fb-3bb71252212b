<component name="libraryTable">
  <library name="Gradle: io.micrometer:micrometer-core:1.15.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.micrometer" artifactId="micrometer-core" version="1.15.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-core/1.15.1/52959bd15d21f9a9dd55ac59f0149b16c6d5a06e/micrometer-core-1.15.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-core/1.15.1/219e41b1370b20368ba8736d23b9271318115291/micrometer-core-1.15.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>