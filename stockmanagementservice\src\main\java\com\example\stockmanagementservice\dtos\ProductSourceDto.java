package com.example.stockmanagementservice.dtos;

import com.example.stockmanagementservice.model.TypeProduit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO pour les produits de type Source
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ProductSourceDto extends ProduitDto {
    
    public ProductSourceDto(Long id, String code, String libelle, String description, 
                           String style, Boolean defaultReclamation, Boolean export) {
        super(id, code, libelle, description, style, defaultReclamation, export, TypeProduit.SOURCE);
    }
} 
