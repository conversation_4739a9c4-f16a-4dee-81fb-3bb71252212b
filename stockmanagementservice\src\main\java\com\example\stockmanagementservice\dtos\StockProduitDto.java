package com.example.stockmanagementservice.dtos;

/**
 * DTO simple pour les relations stock-produit
 */
public class StockProduitDto {
    
    private Long id;
    private Long stockId;
    private Long produitId;
    private Integer quantite;
    
    // Constructeurs
    public StockProduitDto() {}
    
    public StockProduitDto(Long id, Long stockId, Long produitId, Integer quantite) {
        this.id = id;
        this.stockId = stockId;
        this.produitId = produitId;
        this.quantite = quantite;
    }
    
    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getStockId() { return stockId; }
    public void setStockId(Long stockId) { this.stockId = stockId; }
    
    public Long getProduitId() { return produitId; }
    public void setProduitId(Long produitId) { this.produitId = produitId; }
    
    public Integer getQuantite() { return quantite; }
    public void setQuantite(Integer quantite) { this.quantite = quantite; }
}
