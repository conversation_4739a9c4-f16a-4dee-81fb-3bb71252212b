package com.example.stockmanagementservice.dtos;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO pour la relation Stock-Produit
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockProduitDto {
    
    private Long id;
    
    @NotNull(message = "L'ID du stock est obligatoire")
    private Long stockId;
    
    @NotNull(message = "L'ID du produit est obligatoire")
    private Long produitId;
    
    // Informations du stock (pour les vues)
    private String stockCode;
    private String stockLibelle;
    
    // Informations du produit (pour les vues)
    private String produitCode;
    private String produitLibelle;
    
    // Liste des stocks journaliers (optionnel, pour les vues détaillées)
    private List<StockJournalierDto> stockJournaliers;
} 
