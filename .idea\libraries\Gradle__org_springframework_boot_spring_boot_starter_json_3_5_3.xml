<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-json:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-json" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-json/3.5.3/fe424c3edf444e65e3c3331a1538d31396225a3b/spring-boot-starter-json-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-json/3.5.3/b13f85e05c320a470373e082d80e6c039f39a55f/spring-boot-starter-json-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>