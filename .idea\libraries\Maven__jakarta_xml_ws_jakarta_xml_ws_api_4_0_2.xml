<component name="libraryTable">
  <library name="Maven: jakarta.xml.ws:jakarta.xml.ws-api:4.0.2" type="java-imported" external-system-id="Maven">
    <properties groupId="jakarta.xml.ws" artifactId="jakarta.xml.ws-api" version="4.0.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/xml/ws/jakarta.xml.ws-api/4.0.2/jakarta.xml.ws-api-4.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>