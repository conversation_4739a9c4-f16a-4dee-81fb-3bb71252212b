<component name="libraryTable">
  <library name="Gradle: org.springframework.data:spring-data-commons:3.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.data" artifactId="spring-data-commons" version="3.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-commons/3.5.1/90bd3f9edcc3b98e0d49657f3f2152e933b42ae8/spring-data-commons-3.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.data/spring-data-commons/3.5.1/d21ac080ad25ebbebb1afe905461c436a87e45a2/spring-data-commons-3.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>