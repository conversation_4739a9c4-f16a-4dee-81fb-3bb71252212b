<component name="libraryTable">
  <library name="Gradle: org.reactivestreams:reactive-streams:1.0.4" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.reactivestreams" artifactId="reactive-streams" version="1.0.4" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.reactivestreams/reactive-streams/1.0.4/3864a1320d97d7b045f729a326e1e077661f31b7/reactive-streams-1.0.4.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.reactivestreams/reactive-streams/1.0.4/d3cddd3497e618c6d3810ef439f13666f889abe4/reactive-streams-1.0.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>