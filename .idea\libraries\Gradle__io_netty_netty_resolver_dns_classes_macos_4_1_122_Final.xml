<component name="libraryTable">
  <library name="Gradle: io.netty:netty-resolver-dns-classes-macos:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-resolver-dns-classes-macos" version="4.1.122.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-classes-macos/4.1.122.Final/2aa5499129e01bc0b1f663ba512a55086d9444b9/netty-resolver-dns-classes-macos-4.1.122.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-classes-macos/4.1.122.Final/a7e7f1c151606e8a43812e509f0a4ca8e3bf11fb/netty-resolver-dns-classes-macos-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>