<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-gateway-server:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-gateway-server" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server/4.3.0/a2a9990714dc9087d9054a38b2e62b828f0dd0e2/spring-cloud-gateway-server-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server/4.3.0/c048a20c764cae8dd0631419f471f416701f3a40/spring-cloud-gateway-server-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>