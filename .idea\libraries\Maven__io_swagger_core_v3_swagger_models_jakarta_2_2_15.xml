<component name="libraryTable">
  <library name="Maven: io.swagger.core.v3:swagger-models-jakarta:2.2.15" type="java-imported" external-system-id="Maven">
    <properties groupId="io.swagger.core.v3" artifactId="swagger-models-jakarta" version="2.2.15" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15-sources.jar!/" />
    </SOURCES>
  </library>
</component>