<component name="libraryTable">
  <library name="Gradle: org.glassfish.hk2:hk2-api:3.1.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.hk2" artifactId="hk2-api" version="3.1.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-api/3.1.1/18d114e0da203809052f5f000803f2db6c9e9cf5/hk2-api-3.1.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.hk2/hk2-api/3.1.1/796a7a89f92f52d7f39c64935daeda9dd40d77b5/hk2-api-3.1.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>