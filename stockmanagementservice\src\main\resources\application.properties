spring.application.name=STOCK-MANAGEMENT-SERVICE

# Configuration H2 pour développement
spring.datasource.url=jdbc:h2:mem:stockdb
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

eureka.client.service-url.defaultZone=http://localhost:8761/eureka/

server.port=8081
