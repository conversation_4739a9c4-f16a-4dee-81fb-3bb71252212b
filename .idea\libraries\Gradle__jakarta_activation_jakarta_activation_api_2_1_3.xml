<component name="libraryTable">
  <library name="Gradle: jakarta.activation:jakarta.activation-api:2.1.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.activation" artifactId="jakarta.activation-api" version="2.1.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.activation/jakarta.activation-api/2.1.3/fa165bd70cda600368eee31555222776a46b881f/jakarta.activation-api-2.1.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.activation/jakarta.activation-api/2.1.3/b65beccd47ff05cf3f46e810d9eebed8d63a3793/jakarta.activation-api-2.1.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>