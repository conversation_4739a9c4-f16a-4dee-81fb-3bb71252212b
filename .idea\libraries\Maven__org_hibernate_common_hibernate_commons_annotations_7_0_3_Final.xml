<component name="libraryTable">
  <library name="Maven: org.hibernate.common:hibernate-commons-annotations:7.0.3.Final" type="java-imported" external-system-id="Maven">
    <properties groupId="org.hibernate.common" artifactId="hibernate-commons-annotations" version="7.0.3.Final" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>