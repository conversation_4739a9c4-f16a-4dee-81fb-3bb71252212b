<component name="libraryTable">
  <library name="Maven: org.checkerframework:checker-qual:3.49.3" type="java-imported" external-system-id="Maven">
    <properties groupId="org.checkerframework" artifactId="checker-qual" version="3.49.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/checkerframework/checker-qual/3.49.3/checker-qual-3.49.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>