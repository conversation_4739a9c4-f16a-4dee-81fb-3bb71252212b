package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.ProduitDto;
import com.example.stockmanagementservice.model.Produit;
import com.example.stockmanagementservice.services.ProduitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des produits
 */
@RestController
@RequestMapping("/api/produits")
public class ProduitController {
    
    @Autowired
    private ProduitService produitService;
    
    // Créer un produit
    @PostMapping
    public ResponseEntity<ProduitDto> createProduit(@RequestBody ProduitDto produitDto) {
        try {
            Produit produit = produitService.convertToEntity(produitDto);
            Produit savedProduit = produitService.createProduit(produit);
            ProduitDto responseDto = produitService.convertToDto(savedProduit);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer tous les produits
    @GetMapping
    public ResponseEntity<List<ProduitDto>> getAllProduits() {
        try {
            List<Produit> produits = produitService.getAllProduits();
            List<ProduitDto> produitsDto = produits.stream()
                .map(produitService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(produitsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un produit par ID
    @GetMapping("/{id}")
    public ResponseEntity<ProduitDto> getProduitById(@PathVariable Long id) {
        try {
            Optional<Produit> produit = produitService.getProduitById(id);
            if (produit.isPresent()) {
                ProduitDto produitDto = produitService.convertToDto(produit.get());
                return new ResponseEntity<>(produitDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un produit par code
    @GetMapping("/code/{code}")
    public ResponseEntity<ProduitDto> getProduitByCode(@PathVariable String code) {
        try {
            Optional<Produit> produit = produitService.getProduitByCode(code);
            if (produit.isPresent()) {
                ProduitDto produitDto = produitService.convertToDto(produit.get());
                return new ResponseEntity<>(produitDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Rechercher par libellé
    @GetMapping("/search")
    public ResponseEntity<List<ProduitDto>> searchProduits(@RequestParam String libelle) {
        try {
            List<Produit> produits = produitService.searchByLibelle(libelle);
            List<ProduitDto> produitsDto = produits.stream()
                .map(produitService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(produitsDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour un produit
    @PutMapping("/{id}")
    public ResponseEntity<ProduitDto> updateProduit(@PathVariable Long id, @RequestBody ProduitDto produitDto) {
        try {
            Produit produitDetails = produitService.convertToEntity(produitDto);
            Produit updatedProduit = produitService.updateProduit(id, produitDetails);
            if (updatedProduit != null) {
                ProduitDto responseDto = produitService.convertToDto(updatedProduit);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer un produit
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteProduit(@PathVariable Long id) {
        try {
            boolean deleted = produitService.deleteProduit(id);
            if (deleted) {
                return new ResponseEntity<>("Produit supprimé avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Produit non trouvé", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
