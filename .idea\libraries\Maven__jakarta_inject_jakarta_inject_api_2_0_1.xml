<component name="libraryTable">
  <library name="Maven: jakarta.inject:jakarta.inject-api:2.0.1" type="java-imported" external-system-id="Maven">
    <properties groupId="jakarta.inject" artifactId="jakarta.inject-api" version="2.0.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>