<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.core:jackson-databind:2.19.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-databind" version="2.19.1" />
    <ANNOTATIONS>
      <root url="jar://$MAVEN_REPOSITORY$/org/jetbrains/externalAnnotations/com/fasterxml/jackson/core/jackson-databind/2.9.6-an1/jackson-databind-2.9.6-an1-annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.19.1/e8cb8e76faea3e0791165f5d3614fc45933b2ee0/jackson-databind-2.19.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.19.1/dd9a5f49c1e722518406c238d6cdaa1574c1cf61/jackson-databind-2.19.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>