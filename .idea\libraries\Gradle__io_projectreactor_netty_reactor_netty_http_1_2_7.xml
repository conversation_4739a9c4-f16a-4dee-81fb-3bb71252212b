<component name="libraryTable">
  <library name="Gradle: io.projectreactor.netty:reactor-netty-http:1.2.7" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.projectreactor.netty" artifactId="reactor-netty-http" version="1.2.7" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.projectreactor.netty/reactor-netty-http/1.2.7/583002ce7ab598ff463ff2673af6bddf2e31083b/reactor-netty-http-1.2.7.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.projectreactor.netty/reactor-netty-http/1.2.7/92b16d89d7242160628201c5ddec0ec123322f2a/reactor-netty-http-1.2.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>