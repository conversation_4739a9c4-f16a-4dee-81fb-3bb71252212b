<component name="libraryTable">
  <library name="Gradle: io.github.x-stream:mxparser:1.2.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.github.x-stream" artifactId="mxparser" version="1.2.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.github.x-stream/mxparser/1.2.2/476fb3b3bb3716cad797cd054ce45f89445794e9/mxparser-1.2.2.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.github.x-stream/mxparser/1.2.2/7474ae992cb9fcfb25c9aa4826e39fb5957f47e6/mxparser-1.2.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>