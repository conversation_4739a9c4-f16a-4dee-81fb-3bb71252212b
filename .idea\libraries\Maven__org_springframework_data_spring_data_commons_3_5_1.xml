<component name="libraryTable">
  <library name="Maven: org.springframework.data:spring-data-commons:3.5.1" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework.data" artifactId="spring-data-commons" version="3.5.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>