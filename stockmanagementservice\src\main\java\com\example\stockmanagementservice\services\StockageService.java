package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.StockageDto;
import com.example.stockmanagementservice.model.Stockage;
import com.example.stockmanagementservice.repositories.StockageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des stockages - CRUD de base
 */
@Service
public class StockageService {
    
    @Autowired
    private StockageRepository stockageRepository;
    
    // Créer un stockage
    public Stockage createStockage(Stockage stockage) {
        return stockageRepository.save(stockage);
    }
    
    // Récupérer tous les stockages
    public List<Stockage> getAllStockages() {
        return stockageRepository.findAll();
    }
    
    // Récupérer un stockage par ID
    public Optional<Stockage> getStockageById(Long id) {
        return stockageRepository.findById(id);
    }
    
    // Récupérer un stockage par code
    public Optional<Stockage> getStockageByCode(String code) {
        return stockageRepository.findByCode(code);
    }
    
    // Rechercher par libellé
    public List<Stockage> searchByLibelle(String libelle) {
        return stockageRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    // Mettre à jour un stockage
    public Stockage updateStockage(Long id, Stockage stockageDetails) {
        Optional<Stockage> optionalStockage = stockageRepository.findById(id);
        if (optionalStockage.isPresent()) {
            Stockage stockage = optionalStockage.get();
            stockage.setCode(stockageDetails.getCode());
            stockage.setLibelle(stockageDetails.getLibelle());
            stockage.setDescription(stockageDetails.getDescription());
            return stockageRepository.save(stockage);
        }
        return null;
    }
    
    // Supprimer un stockage
    public boolean deleteStockage(Long id) {
        if (stockageRepository.existsById(id)) {
            stockageRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Stockages ordonnés par libellé
    public List<Stockage> getStockagesOrderedByLibelle() {
        return stockageRepository.findAllByOrderByLibelleAsc();
    }
    
    // Convertir entité vers DTO
    public StockageDto convertToDto(Stockage stockage) {
        return new StockageDto(
            stockage.getId(),
            stockage.getCode(),
            stockage.getLibelle(),
            stockage.getDescription()
        );
    }
    
    // Convertir DTO vers entité
    public Stockage convertToEntity(StockageDto dto) {
        Stockage stockage = new Stockage();
        stockage.setId(dto.getId());
        stockage.setCode(dto.getCode());
        stockage.setLibelle(dto.getLibelle());
        stockage.setDescription(dto.getDescription());
        return stockage;
    }
}
