package com.example.stockmanagementservice.dtos;

import com.example.stockmanagementservice.model.TypeProduit;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO de base pour les produits
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "typeProduit")
@JsonSubTypes({
    @JsonSubTypes.Type(value = ProductMarchandDto.class, name = "MARCHAND"),
    @JsonSubTypes.Type(value = ProductSourceDto.class, name = "SOURCE")
})
public abstract class ProduitDto {
    
    private Long id;
    
    @NotBlank(message = "Le code est obligatoire")
    @Size(max = 255, message = "Le code ne peut pas dépasser 255 caractères")
    private String code;
    
    @NotBlank(message = "Le libellé est obligatoire")
    @Size(max = 255, message = "Le libellé ne peut pas dépasser 255 caractères")
    private String libelle;
    
    @Size(max = 500, message = "La description ne peut pas dépasser 500 caractères")
    private String description;
    
    @Size(max = 255, message = "Le style ne peut pas dépasser 255 caractères")
    private String style;
    
    private Boolean defaultReclamation = false;
    
    private Boolean export = false;
    
    private TypeProduit typeProduit;
} 
