<component name="libraryTable">
  <library name="Gradle: jakarta.transaction:jakarta.transaction-api:2.0.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.transaction" artifactId="jakarta.transaction-api" version="2.0.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.transaction/jakarta.transaction-api/2.0.1/51a520e3fae406abb84e2e1148e6746ce3f80a1a/jakarta.transaction-api-2.0.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.transaction/jakarta.transaction-api/2.0.1/660c982b9f5a7c8a6613c84f6e1c8b9221a4c574/jakarta.transaction-api-2.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>