<component name="libraryTable">
  <library name="Maven: org.hibernate.validator:hibernate-validator:8.0.2.Final" type="java-imported" external-system-id="Maven">
    <properties groupId="org.hibernate.validator" artifactId="hibernate-validator" version="8.0.2.Final" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>