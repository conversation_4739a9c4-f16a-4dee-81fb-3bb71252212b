<component name="libraryTable">
  <library name="Maven: org.bouncycastle:bcprov-jdk18on:1.80" type="java-imported" external-system-id="Maven">
    <properties groupId="org.bouncycastle" artifactId="bcprov-jdk18on" version="1.80" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80-sources.jar!/" />
    </SOURCES>
  </library>
</component>