<component name="libraryTable">
  <library name="Gradle: io.netty:netty-buffer:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-buffer" version="4.1.122.Final" />
    <ANNOTATIONS>
      <root url="jar://$MAVEN_REPOSITORY$/org/jetbrains/externalAnnotations/io/netty/netty-buffer/4.1.27.Final-an1/netty-buffer-4.1.27.Final-an1-annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.122.Final/d5798d560039426a5c8c69fbe187c0f8cb6ec0ae/netty-buffer-4.1.122.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.122.Final/ccce5cdbd2fc18482b037a63a43e4c83ed24c43e/netty-buffer-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>