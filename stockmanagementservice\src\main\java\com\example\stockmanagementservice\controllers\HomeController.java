package com.example.stockmanagementservice.controllers;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Contrôleur principal pour tester l'API
 */
@RestController
@RequestMapping("/api")
public class HomeController {
    
    @GetMapping
    public Map<String, Object> home() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "Stock Management Service");
        response.put("version", "1.0.0");
        response.put("status", "UP");
        response.put("endpoints", Map.of(
            "produits", "/api/produits",
            "stocks", "/api/stocks", 
            "stockages", "/api/stockages",
            "stock-produits", "/api/stock-produits"
        ));
        return response;
    }
    
    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Stock Management Service");
        return response;
    }
}
