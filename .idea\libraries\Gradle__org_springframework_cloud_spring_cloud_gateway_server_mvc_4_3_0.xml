<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-gateway-server-mvc:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-gateway-server-mvc" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server-mvc/4.3.0/1db6ff33d759e2c538582b79dd281c805e8483c8/spring-cloud-gateway-server-mvc-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server-mvc/4.3.0/3faee3cf4666ea248de0c3e040f71e2b1204fe98/spring-cloud-gateway-server-mvc-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>