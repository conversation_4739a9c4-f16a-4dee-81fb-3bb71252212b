<component name="libraryTable">
  <library name="Gradle: jakarta.ws.rs:jakarta.ws.rs-api:3.1.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="jakarta.ws.rs" artifactId="jakarta.ws.rs-api" version="3.1.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.ws.rs/jakarta.ws.rs-api/3.1.0/15ce10d249a38865b58fc39521f10f29ab0e3363/jakarta.ws.rs-api-3.1.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/jakarta.ws.rs/jakarta.ws.rs-api/3.1.0/f0816bb784efd91e181a62057362ef2acd91fc9c/jakarta.ws.rs-api-3.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>