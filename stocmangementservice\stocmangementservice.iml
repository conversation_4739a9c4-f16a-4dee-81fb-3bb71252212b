<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id="$MODULE_DIR$/pom.xml" external.system.module.type="SINGLE_MODULE" external.system.module.version="223-2" org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet external-system-id="Maven" type="web" name="Web">
      <configuration>
        <webroots />
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_21">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/target/generated-sources/annotations" isTestSource="false" generated="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="jdk" jdkName="21" jdkType="JavaSDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.5.18" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.5.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.24.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.24.3" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:2.0.17" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:2.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:10.1.42" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:8.0.2.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:3.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.6.1.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.19.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.19.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.19.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:10.1.42" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:10.1.42" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-observation:1.15.1" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-commons:1.15.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web-services:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.messaging.saaj:saaj-impl:3.0.4" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.soap:jakarta.xml.soap-api:3.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.jvnet.staxex:stax-ex:2.1.0" level="project" />
    <orderEntry type="library" name="Maven: jakarta.activation:jakarta.activation-api:2.1.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.eclipse.angus:angus-activation:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.ws:jakarta.xml.ws-api:4.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.ws:spring-ws-core:4.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.ws:spring-xml:4.1.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:jaxb-runtime:4.0.5" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:jaxb-core:4.0.5" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:txw2:4.0.5" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.istack:istack-commons-runtime:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-jpa:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:6.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.orm:hibernate-core:6.6.18.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.transaction:jakarta.transaction-api:2.0.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.hibernate.common:hibernate-commons-annotations:7.0.3.Final" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: io.smallrye:jandex:3.2.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: net.bytebuddy:byte-buddy:1.17.6" level="project" />
    <orderEntry type="library" name="Maven: jakarta.inject:jakarta.inject-api:2.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr4-runtime:4.13.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-jpa:3.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:3.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-orm:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:2.0.17" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aspects:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:6.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk18on:1.80" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-netflix-eureka-client:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents.client5:httpclient5:5.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents.core5:httpcore5:5.3.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents.core5:httpcore5-h2:5.3.4" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.eureka:eureka-client:2.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.thoughtworks.xstream:xstream:1.4.20" level="project" />
    <orderEntry type="library" name="Maven: io.github.x-stream:mxparser:1.2.2" level="project" />
    <orderEntry type="library" name="Maven: xmlpull:xmlpull:1.1.3.1" level="project" />
    <orderEntry type="library" name="Maven: jakarta.ws.rs:jakarta.ws.rs-api:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: com.netflix.spectator:spectator-api:1.7.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.16" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.18.0" level="project" />
    <orderEntry type="library" name="Maven: commons-configuration:commons-configuration:1.10" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.19.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.19.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-eventbus:0.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.netflix-commons:netflix-infix:0.3.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: commons-jxpath:commons-jxpath:1.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: joda-time:joda-time:2.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.antlr:antlr-runtime:3.4" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.antlr:stringtemplate:3.2.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: antlr:antlr:2.7.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.google.guava:guava:14.0.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.google.code.gson:gson:2.13.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.google.errorprone:error_prone_annotations:2.38.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.netflix.servo:servo-core:0.5.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.apache.commons:commons-math:2.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: javax.annotation:javax.annotation-api:1.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.codehaus.jettison:jettison:1.5.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-loadbalancer:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-loadbalancer:4.3.0" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor.addons:reactor-extra:3.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-cache:3.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: com.stoyanr:evictor:1.0.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.postgresql:postgresql:42.7.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.checkerframework:checker-qual:3.49.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.h2database:h2:2.3.232" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-starter-webmvc-api:2.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-starter-common:2.2.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-core-jakarta:2.2.15" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.17.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-annotations-jakarta:2.2.15" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-models-jakarta:2.2.15" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.19.1" level="project" />
    <orderEntry type="library" name="Maven: org.webjars:swagger-ui:5.2.0" level="project" />
    <orderEntry type="library" name="Maven: jakarta.persistence:jakarta.persistence-api:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.30" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:3.5.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:3.5.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:3.5.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:4.0.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.5.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:2.5.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:9.7.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.27.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.awaitility:awaitility:4.2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest:3.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter:5.12.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-api:5.12.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.opentest4j:opentest4j:1.3.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-commons:1.12.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.apiguardian:apiguardian-api:1.1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-params:5.12.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.12.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-engine:1.12.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:5.17.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.17.6" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:3.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-junit-jupiter:5.17.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:6.2.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:6.2.8" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:6.2.8" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.10.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: io.projectreactor:reactor-test:3.7.7" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.4" level="project" />
  </component>
</module>