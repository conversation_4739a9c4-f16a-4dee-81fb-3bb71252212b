<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-data-jpa:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-data-jpa" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-data-jpa/3.5.3/8494346cf0bdb148ef29d5f41647b0cd6908ad7d/spring-boot-starter-data-jpa-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-data-jpa/3.5.3/8494346cf0bdb148ef29d5f41647b0cd6908ad7d/spring-boot-starter-data-jpa-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>