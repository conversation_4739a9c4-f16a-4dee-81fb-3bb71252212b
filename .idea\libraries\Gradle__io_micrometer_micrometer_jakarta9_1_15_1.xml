<component name="libraryTable">
  <library name="Gradle: io.micrometer:micrometer-jakarta9:1.15.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.micrometer" artifactId="micrometer-jakarta9" version="1.15.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-jakarta9/1.15.1/767dc009dd6c41cc574013ebb006cddfe98e88e6/micrometer-jakarta9-1.15.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.micrometer/micrometer-jakarta9/1.15.1/8f7eb8b06ac9275f34c0639abcef9ef899205ff7/micrometer-jakarta9-1.15.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>