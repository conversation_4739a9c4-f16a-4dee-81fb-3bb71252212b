<component name="libraryTable">
  <library name="Maven: com.google.code.gson:gson:2.13.1" type="java-imported" external-system-id="Maven">
    <properties groupId="com.google.code.gson" artifactId="gson" version="2.13.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/google/code/gson/gson/2.13.1/gson-2.13.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/google/code/gson/gson/2.13.1/gson-2.13.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/google/code/gson/gson/2.13.1/gson-2.13.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>