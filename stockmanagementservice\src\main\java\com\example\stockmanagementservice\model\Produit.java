package com.example.stockmanagementservice.model;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "produit")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type_produit", discriminatorType = DiscriminatorType.STRING)
public abstract class Produit  {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    @Column(name = "code", updatable = false, nullable = false, unique = true)
    protected String code;

    @Column(name = "libelle")
    protected String libelle;

    @Column(name = "description")
    protected String description;

    @Column(name = "style")
    protected String style;

    protected Boolean defaultReclamation = false;
    protected Boolean export = false;

    @OneToMany(mappedBy = "produit", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StockProduit> stockProduits;

    @OneToMany(mappedBy ="produit",cascade = CascadeType.ALL,fetch=FetchType.LAZY)
    private  List<StockJournalier> stockJournaliers;

    // Constructeur par défaut
    public Produit() {
    }

    public Produit(long id) {
        this.id = id;
    }

    public Produit(long id, String libelle) {
        this.id = id;
        this.libelle = libelle;
    }

    public Produit(String libelle) {
        this.libelle = libelle;
    }

    public Produit(String libelle, String code) {
        this.libelle = libelle;
        this.code = code;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Produit produit = (Produit) o;
        return Objects.equals(id, produit.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }
} 
