<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.19.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.dataformat" artifactId="jackson-dataformat-xml" version="2.19.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-xml/2.19.1/aa0165cd647b6fbd2a69e4bf8cd10d54ec70304/jackson-dataformat-xml-2.19.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.dataformat/jackson-dataformat-xml/2.19.1/e2ef6462815e3018754a0348aa39a7eb2500528a/jackson-dataformat-xml-2.19.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>