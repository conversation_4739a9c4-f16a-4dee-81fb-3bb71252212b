<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-test:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-test" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-test/6.5.1/711bd65cfd6f4444293eee1c8d5346def3d790b9/spring-security-test-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-test/6.5.1/e161e1fdca57b2aaa5f44169847c79e43504546d/spring-security-test-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>