package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.StockJournalier;
import com.example.stockmanagementservice.model.StockProduit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité StockJournalier (suivi quotidien des stocks)
 */
@Repository
public interface StockJournalierRepository extends JpaRepository<StockJournalier, Long> {
    
    /**
     * Recherche par StockProduit
     */
    List<StockJournalier> findByStockProduit(StockProduit stockProduit);
    
    /**
     * Recherche par ID de StockProduit
     */
    List<StockJournalier> findByStockProduitId(Long stockProduitId);
    
    /**
     * Recherche par date
     */
    List<StockJournalier> findByJour(LocalDate jour);
    
    /**
     * Recherche par StockProduit et date (unique)
     */
    Optional<StockJournalier> findByStockProduitAndJour(StockProduit stockProduit, LocalDate jour);
    
    /**
     * Vérifier l'existence par StockProduit et date
     */
    boolean existsByStockProduitAndJour(StockProduit stockProduit, LocalDate jour);
    
    /**
     * Recherche par plage de dates
     */
    List<StockJournalier> findByJourBetween(LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Recherche par StockProduit et plage de dates
     */
    List<StockJournalier> findByStockProduitAndJourBetween(
        StockProduit stockProduit, 
        LocalDate dateDebut, 
        LocalDate dateFin
    );
    
    /**
     * Recherche par quantité minimale
     */
    List<StockJournalier> findByQuantiteGreaterThanEqual(java.math.BigDecimal quantiteMin);
    
    /**
     * Recherche par quantité nulle (rupture de stock)
     */
    List<StockJournalier> findByQuantiteEquals(java.math.BigDecimal quantite);
    
    /**
     * Ruptures de stock (quantité = 0)
     */
    @Query("SELECT sj FROM StockJournalier sj WHERE sj.quantite = 0")
    List<StockJournalier> findRupturesStock();
    
    /**
     * Stocks faibles (quantité <= seuil)
     */
    @Query("SELECT sj FROM StockJournalier sj WHERE sj.quantite <= :seuil")
    List<StockJournalier> findStocksFaibles(@Param("seuil") java.math.BigDecimal seuil);
    
    /**
     * Recherche multicritères avec pagination
     */
    @Query("SELECT sj FROM StockJournalier sj WHERE " +
           "(:stockProduitId IS NULL OR sj.stockProduit.id = :stockProduitId) AND " +
           "(:dateDebut IS NULL OR sj.jour >= :dateDebut) AND " +
           "(:dateFin IS NULL OR sj.jour <= :dateFin) AND " +
           "(:quantiteMin IS NULL OR sj.quantite >= :quantiteMin) AND " +
           "(:quantiteMax IS NULL OR sj.quantite <= :quantiteMax)")
    Page<StockJournalier> findBySearchCriteria(
        @Param("stockProduitId") Long stockProduitId,
        @Param("dateDebut") LocalDate dateDebut,
        @Param("dateFin") LocalDate dateFin,
        @Param("quantiteMin") java.math.BigDecimal quantiteMin,
        @Param("quantiteMax") java.math.BigDecimal quantiteMax,
        Pageable pageable
    );
    
    /**
     * Dernier stock journalier par StockProduit
     */
    @Query("SELECT sj FROM StockJournalier sj WHERE sj.stockProduit = :stockProduit " +
           "AND sj.jour = (SELECT MAX(sj2.jour) FROM StockJournalier sj2 WHERE sj2.stockProduit = :stockProduit)")
    Optional<StockJournalier> findLatestByStockProduit(@Param("stockProduit") StockProduit stockProduit);
    
    /**
     * Historique ordonné par date décroissante
     */
    List<StockJournalier> findByStockProduitOrderByJourDesc(StockProduit stockProduit);
    
    /**
     * Évolution des stocks sur une période
     */
    @Query("SELECT sj FROM StockJournalier sj WHERE sj.jour BETWEEN :dateDebut AND :dateFin " +
           "ORDER BY sj.jour ASC")
    List<StockJournalier> findEvolutionStocks(
        @Param("dateDebut") LocalDate dateDebut, 
        @Param("dateFin") LocalDate dateFin
    );
    
    /**
     * Moyenne des quantités par StockProduit sur une période
     */
    @Query("SELECT sj.stockProduit, AVG(sj.quantite) FROM StockJournalier sj " +
           "WHERE sj.jour BETWEEN :dateDebut AND :dateFin " +
           "GROUP BY sj.stockProduit")
    List<Object[]> findMoyenneQuantitesByPeriode(
        @Param("dateDebut") LocalDate dateDebut, 
        @Param("dateFin") LocalDate dateFin
    );
    
    /**
     * Stock journalier du jour pour tous les StockProduits
     */
    @Query("SELECT sj FROM StockJournalier sj WHERE sj.jour = :date")
    List<StockJournalier> findStocksDuJour(@Param("date") LocalDate date);
    
    /**
     * Recherche par expédition
     */
    List<StockJournalier> findByExpedition(Boolean expedition);
    
    /**
     * Recherche par code
     */
    List<StockJournalier> findByCodeContainingIgnoreCase(String code);
    
    /**
     * Recherche par libellé
     */
    List<StockJournalier> findByLibelleContainingIgnoreCase(String libelle);
} 
