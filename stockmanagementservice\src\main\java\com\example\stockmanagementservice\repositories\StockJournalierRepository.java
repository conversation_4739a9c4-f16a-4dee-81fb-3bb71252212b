package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.StockJournalier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository simple pour l'entité StockJournalier - CRUD de base
 */
@Repository
public interface StockJournalierRepository extends JpaRepository<StockJournalier, Long> {
    
    // Recherche par date
    List<StockJournalier> findByDate(LocalDate date);
    
    // Recherche par période
    List<StockJournalier> findByDateBetween(LocalDate startDate, LocalDate endDate);
    
    // Recherche par stock ID
    List<StockJournalier> findByStockId(Long stockId);
    
    // Recherche par produit ID
    List<StockJournalier> findByProduitId(Long produitId);
    
    // Recherche par stock et date
    List<StockJournalier> findByStockIdAndDate(Long stockId, LocalDate date);
    
    // Stocks journaliers ordonnés par date
    List<StockJournalier> findAllByOrderByDateDesc();
}
