<component name="libraryTable">
  <library name="Gradle: org.junit.jupiter:junit-jupiter:5.12.2" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.junit.jupiter" artifactId="junit-jupiter" version="5.12.2" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.12.2/64d3ec64ae06517bc14b7faaaec7c996b0d05bea/junit-jupiter-5.12.2.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.12.2/9c1abe3ede704e951fa5329365daa7a4cb444ed5/junit-jupiter-5.12.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>