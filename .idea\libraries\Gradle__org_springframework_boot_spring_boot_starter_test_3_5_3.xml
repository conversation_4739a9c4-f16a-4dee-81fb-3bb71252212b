<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-test:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-test" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/3.5.3/916e174221d2b9c770ecc12c8adc2bf2b75ff49/spring-boot-starter-test-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/3.5.3/ec440c6aceb588def7ac155c6ecba178b4770e7d/spring-boot-starter-test-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>