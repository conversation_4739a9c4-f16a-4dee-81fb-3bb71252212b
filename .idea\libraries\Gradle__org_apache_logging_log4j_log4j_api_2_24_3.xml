<component name="libraryTable">
  <library name="Gradle: org.apache.logging.log4j:log4j-api:2.24.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.apache.logging.log4j" artifactId="log4j-api" version="2.24.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-api/2.24.3/b02c125db8b6d295adf72ae6e71af5d83bce2370/log4j-api-2.24.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.apache.logging.log4j/log4j-api/2.24.3/a9741c71e05aab4f5624a1cb3c1cec6d833b8701/log4j-api-2.24.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>