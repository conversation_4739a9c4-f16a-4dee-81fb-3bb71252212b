<component name="libraryTable">
  <library name="Maven: javax.annotation:javax.annotation-api:1.2" type="java-imported" external-system-id="Maven">
    <properties groupId="javax.annotation" artifactId="javax.annotation-api" version="1.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>