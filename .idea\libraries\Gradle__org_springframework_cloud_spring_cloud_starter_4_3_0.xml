<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-starter:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-starter/4.3.0/8e794c3b8438450bf8d7cfbb936bb05344a54397/spring-cloud-starter-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>