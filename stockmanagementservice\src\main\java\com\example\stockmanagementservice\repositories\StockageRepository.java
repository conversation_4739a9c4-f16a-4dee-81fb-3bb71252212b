package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.Stockage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface StockageRepository extends JpaRepository<Stockage, Long> {

    // Vérifier existence par code
    boolean existsByCode(String code);

    // Rechercher par code exact
    Optional<Stockage> findByCode(String code);

    // Rechercher par code contenant (insensible à la casse)
    List<Stockage> findByCodeContainingIgnoreCase(String code);

    // Rechercher par libelle contenant (insensible à la casse)
    List<Stockage> findByLibelleContainingIgnoreCase(String libelle);

    // Rechercher par libelle OU code contenant
    Page<Stockage> findByLibelleContainingIgnoreCaseOrCodeContainingIgnoreCase(
            String libelle, String code, Pageable pageable);

    // Compter le nombre de stocks associés à un stockage
    @Query("SELECT COUNT(s) FROM Stock s WHERE s.stockage.id = :stockageId")
    long countStocksByStockageId(@Param("stockageId") Long stockageId);

    // Obtenir les stockages ayant au moins un stock
    @Query("SELECT st FROM Stockage st WHERE SIZE(st.stocks) > 0")
    List<Stockage> findStockagesWithStocks();

    // Obtenir les stockages n'ayant aucun stock
    @Query("SELECT st FROM Stockage st WHERE SIZE(st.stocks) = 0")
    List<Stockage> findStockagesWithoutStocks();

    // Recherche multicritères dynamique (libelle + code)
    @Query("SELECT s FROM Stockage s WHERE " +
            "(:code IS NULL OR LOWER(s.code) LIKE LOWER(CONCAT('%', :code, '%'))) AND " +
            "(:libelle IS NULL OR LOWER(s.libelle) LIKE LOWER(CONCAT('%', :libelle, '%')))")
    Page<Stockage> findBySearchCriteria(@Param("code") String code,
                                        @Param("libelle") String libelle,
                                        Pageable pageable);
}
