package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.Stockage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository simple pour l'entité Stockage - CRUD de base
 */
@Repository
public interface StockageRepository extends JpaRepository<Stockage, Long> {
    
    // Recherche par code
    Optional<Stockage> findByCode(String code);
    
    // Recherche par libellé
    List<Stockage> findByLibelleContainingIgnoreCase(String libelle);
    
    // Vérifier l'existence par code
    boolean existsByCode(String code);
    
    // Stockages ordonnés par libellé
    List<Stockage> findAllByOrderByLibelleAsc();
}
