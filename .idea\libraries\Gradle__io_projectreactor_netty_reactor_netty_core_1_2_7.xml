<component name="libraryTable">
  <library name="Gradle: io.projectreactor.netty:reactor-netty-core:1.2.7" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.projectreactor.netty" artifactId="reactor-netty-core" version="1.2.7" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.projectreactor.netty/reactor-netty-core/1.2.7/43515aa4b2f4bce7c431145e8c0a7bcc441e0532/reactor-netty-core-1.2.7.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.projectreactor.netty/reactor-netty-core/1.2.7/b294af1ea6c0122010e4cf09be2d9e1b5c5c85e7/reactor-netty-core-1.2.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>