<component name="libraryTable">
  <library name="Maven: org.apache.httpcomponents:httpcore:4.4.16" type="java-imported" external-system-id="Maven">
    <properties groupId="org.apache.httpcomponents" artifactId="httpcore" version="4.4.16" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16-sources.jar!/" />
    </SOURCES>
  </library>
</component>