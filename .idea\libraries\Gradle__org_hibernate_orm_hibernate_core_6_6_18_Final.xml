<component name="libraryTable">
  <library name="Gradle: org.hibernate.orm:hibernate-core:6.6.18.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.hibernate.orm" artifactId="hibernate-core" version="6.6.18.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hibernate.orm/hibernate-core/6.6.18.Final/c3d150af4c341a7b07c75a8905bfbba895e73fbc/hibernate-core-6.6.18.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.hibernate.orm/hibernate-core/6.6.18.Final/a235e4f1dc0d0e06c4b6fbbf01b22edd6be7fcd3/hibernate-core-6.6.18.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>