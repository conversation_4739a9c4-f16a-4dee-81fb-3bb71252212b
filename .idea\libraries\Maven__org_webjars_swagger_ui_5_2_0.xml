<component name="libraryTable">
  <library name="Maven: org.webjars:swagger-ui:5.2.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.webjars" artifactId="swagger-ui" version="5.2.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>