<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-configuration-metadata:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-configuration-metadata" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-metadata/3.5.3/aaef6c7d09b7bc99a27864dfcae109d1c5c89ba3/spring-boot-configuration-metadata-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-metadata/3.5.3/8cf7ca355337963030ca8a3add1ce6452cb7c32a/spring-boot-configuration-metadata-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>