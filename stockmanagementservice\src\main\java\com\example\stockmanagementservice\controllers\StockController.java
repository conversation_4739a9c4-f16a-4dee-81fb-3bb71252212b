package com.example.stockmanagementservice.controllers;

import com.example.stockmanagementservice.dtos.StockDto;
import com.example.stockmanagementservice.model.Stock;
import com.example.stockmanagementservice.services.StockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Contrôleur simple pour la gestion des stocks
 */
@RestController
@RequestMapping("/api/stocks")
public class StockController {
    
    @Autowired
    private StockService stockService;
    
    // Créer un stock
    @PostMapping
    public ResponseEntity<StockDto> createStock(@RequestBody StockDto stockDto) {
        try {
            Stock stock = stockService.convertToEntity(stockDto);
            Stock savedStock = stockService.createStock(stock);
            StockDto responseDto = stockService.convertToDto(savedStock);
            return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Récupérer tous les stocks
    @GetMapping
    public ResponseEntity<List<StockDto>> getAllStocks() {
        try {
            List<Stock> stocks = stockService.getAllStocks();
            List<StockDto> stocksDto = stocks.stream()
                .map(stockService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stocksDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un stock par ID
    @GetMapping("/{id}")
    public ResponseEntity<StockDto> getStockById(@PathVariable Long id) {
        try {
            Optional<Stock> stock = stockService.getStockById(id);
            if (stock.isPresent()) {
                StockDto stockDto = stockService.convertToDto(stock.get());
                return new ResponseEntity<>(stockDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer un stock par code
    @GetMapping("/code/{code}")
    public ResponseEntity<StockDto> getStockByCode(@PathVariable String code) {
        try {
            Optional<Stock> stock = stockService.getStockByCode(code);
            if (stock.isPresent()) {
                StockDto stockDto = stockService.convertToDto(stock.get());
                return new ResponseEntity<>(stockDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Rechercher par libellé
    @GetMapping("/search")
    public ResponseEntity<List<StockDto>> searchStocks(@RequestParam String libelle) {
        try {
            List<Stock> stocks = stockService.searchByLibelle(libelle);
            List<StockDto> stocksDto = stocks.stream()
                .map(stockService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stocksDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Récupérer les stocks ordonnés par libellé
    @GetMapping("/ordered")
    public ResponseEntity<List<StockDto>> getStocksOrdered() {
        try {
            List<Stock> stocks = stockService.getStocksOrderedByLibelle();
            List<StockDto> stocksDto = stocks.stream()
                .map(stockService::convertToDto)
                .collect(Collectors.toList());
            return new ResponseEntity<>(stocksDto, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // Mettre à jour un stock
    @PutMapping("/{id}")
    public ResponseEntity<StockDto> updateStock(@PathVariable Long id, @RequestBody StockDto stockDto) {
        try {
            Stock stockDetails = stockService.convertToEntity(stockDto);
            Stock updatedStock = stockService.updateStock(id, stockDetails);
            if (updatedStock != null) {
                StockDto responseDto = stockService.convertToDto(updatedStock);
                return new ResponseEntity<>(responseDto, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }
    
    // Supprimer un stock
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteStock(@PathVariable Long id) {
        try {
            boolean deleted = stockService.deleteStock(id);
            if (deleted) {
                return new ResponseEntity<>("Stock supprimé avec succès", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Stock non trouvé", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Erreur lors de la suppression", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
