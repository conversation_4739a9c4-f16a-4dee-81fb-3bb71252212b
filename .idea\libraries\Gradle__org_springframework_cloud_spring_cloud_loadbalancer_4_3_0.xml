<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-loadbalancer:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-loadbalancer" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-loadbalancer/4.3.0/6e2f6d8f91f40e3cfe5cc70205bfde744523a98d/spring-cloud-loadbalancer-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-loadbalancer/4.3.0/60f6e23bb8833439b3f712240eea9960b4006574/spring-cloud-loadbalancer-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>