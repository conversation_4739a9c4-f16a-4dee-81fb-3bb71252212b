<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-starter-gateway-server-webflux:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-gateway-server-webflux" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-starter-gateway-server-webflux/4.3.0/2c7614b286450618cf862090093ae6e66f3e50ca/spring-cloud-starter-gateway-server-webflux-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>