<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.connectors:jersey-apache-connector:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.connectors" artifactId="jersey-apache-connector" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.connectors/jersey-apache-connector/3.1.10/a70d1b085db69e73335696b79fa3be4d7cbb9772/jersey-apache-connector-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.connectors/jersey-apache-connector/3.1.10/ac129e8a77402af462610fb3f2388b3aaec9a5a8/jersey-apache-connector-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>