package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.ProductSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository spécialisé pour ProductSource
 * Hérite automatiquement des méthodes de ProduitRepository via l'héritage JPA
 */
@Repository
public interface ProductSourceRepository extends JpaRepository<ProductSource, Long> {
    
    /**
     * Recherche par code spécifique aux produits sources
     */
    Optional<ProductSource> findByCode(String code);
    
    /**
     * Recherche par libellé pour les produits sources
     */
    List<ProductSource> findByLibelleContainingIgnoreCase(String libelle);
    
    /**
     * Produits sources exportables
     */
    List<ProductSource> findByExportTrue();
    
    /**
     * Produits sources avec réclamation par défaut
     */
    List<ProductSource> findByDefaultReclamationTrue();
    
    /**
     * Recherche par style pour les produits sources
     */
    List<ProductSource> findByStyleContainingIgnoreCase(String style);
    
    /**
     * Compter tous les produits sources
     */
    @Query("SELECT COUNT(ps) FROM ProductSource ps")
    Long countAllProductSource();
    
    /**
     * Produits sources les plus récents
     */
    @Query("SELECT ps FROM ProductSource ps ORDER BY ps.id DESC")
    List<ProductSource> findLatestProductSource();
} 
