package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.ProductSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository simple pour l'entité ProductSource - CRUD de base
 */
@Repository
public interface ProductSourceRepository extends JpaRepository<ProductSource, Long> {
    
    // Recherche par code
    Optional<ProductSource> findByCode(String code);
    
    // Recherche par libellé
    List<ProductSource> findByLibelleContainingIgnoreCase(String libelle);
    
    // Vérifier l'existence par code
    boolean existsByCode(String code);
    
    // Produits ordonnés par libellé
    List<ProductSource> findAllByOrderByLibelleAsc();
}
