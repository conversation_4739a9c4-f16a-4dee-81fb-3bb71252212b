package com.example.stocmangementservice.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Entité représentant le suivi journalier des stocks
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "stock_journalier",
       uniqueConstraints = @UniqueConstraint(columnNames = {"jour", "stock_produit_id"}))
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StockJournalier {
    
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(length = 500)
    private String code;
    
    @Column(length = 500)
    private String libelle;
    
    @Column(columnDefinition = "TEXT")
    private String description;

    
    // Quantité totale
    @Column(precision = 19, scale = 2)
    @PositiveOrZero(message = "La quantité doit être supérieure ou égale à 0")
    private BigDecimal quantite = BigDecimal.ZERO;

    
    @Column(nullable = false)
    private LocalDate jour;
    
    @Column
    private Boolean expedition;
    
    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stock_produit_id", nullable = false)
    private StockProduit stockProduit;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "stockage")
    private Stockage stockage;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "stock")
    private Stock stock;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "produit")
    private Produit produit;


} 