<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-gateway-server-webflux:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-gateway-server-webflux" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server-webflux/4.3.0/7db76e06e7d4b16c8963886318bc62a9bc016990/spring-cloud-gateway-server-webflux-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-gateway-server-webflux/4.3.0/31c10c063e867f03b418f79ccae70e59be95a184/spring-cloud-gateway-server-webflux-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>