package com.example.stocmangementservice.services;

import com.example.stocmangementservice.dtos.StockageDto;
import com.example.stocmangementservice.exceptions.DuplicateCodeException;
import com.example.stocmangementservice.exceptions.InvalidOperationException;
import com.example.stocmangementservice.exceptions.StockageNotFoundException;
import com.example.stocmangementservice.model.Stock;
import com.example.stocmangementservice.model.Stockage;
import com.example.stocmangementservice.repositories.StockageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service pour la gestion des stockages (lieux de stockage)
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class StockageService {
    
    private final StockageRepository stockageRepository;
    
    /**
     * Créer un nouveau stockage
     */
    public Stockage creerStockage(StockageDto stockageDto) {
        log.info("Création d'un nouveau stockage : {}", stockageDto.getCode());
        
        // Vérifier l'unicité du code
        if (stockageRepository.existsByCode(stockageDto.getCode())) {
            throw new DuplicateCodeException("Stockage", stockageDto.getCode());
        }
        
        Stockage stockage = new Stockage();
        stockage.setCode(stockageDto.getCode());
        stockage.setLibelle(stockageDto.getLibelle());
        
        stockage = stockageRepository.save(stockage);
        log.info("Stockage créé avec succès : ID={}, Code={}", stockage.getId(), stockage.getCode());
        
        return stockage;
    }
    
    /**
     * Créer un nouveau stockage à partir d'une entité
     */
    public Stockage creerStockage(Stockage stockage) {
        log.info("Création d'un nouveau stockage : {}", stockage.getCode());
        
        // Vérifier l'unicité du code
        if (stockageRepository.existsByCode(stockage.getCode())) {
            throw new DuplicateCodeException("Stockage", stockage.getCode());
        }
        
        stockage = stockageRepository.save(stockage);
        log.info("Stockage créé avec succès : ID={}, Code={}", stockage.getId(), stockage.getCode());
        
        return stockage;
    }
    
    /**
     * Mettre à jour un stockage existant
     */
    public Stockage modifierStockage(Long id, StockageDto stockageDto) {
        log.info("Mise à jour du stockage ID={}", id);
        
        Stockage stockage = findById(id);
        
        // Vérifier l'unicité du code si changé
        if (!stockage.getCode().equals(stockageDto.getCode()) && 
            stockageRepository.existsByCode(stockageDto.getCode())) {
            throw new DuplicateCodeException("Stockage", stockageDto.getCode());
        }
        
        stockage.setCode(stockageDto.getCode());
        stockage.setLibelle(stockageDto.getLibelle());
        
        stockage = stockageRepository.save(stockage);
        log.info("Stockage mis à jour : ID={}, Code={}", stockage.getId(), stockage.getCode());
        
        return stockage;
    }
    
    /**
     * Mettre à jour un stockage existant (alias pour compatibilité)
     */
    public Stockage mettreAJourStockage(Long id, StockageDto stockageDto) {
        return modifierStockage(id, stockageDto);
    }
    
    /**
     * Mettre à jour un stockage existant à partir d'une entité
     */
    public Stockage modifierStockage(Long id, Stockage stockageData) {
        log.info("Mise à jour du stockage ID={}", id);
        
        Stockage stockage = findById(id);
        
        // Vérifier l'unicité du code si changé
        if (!stockage.getCode().equals(stockageData.getCode()) && 
            stockageRepository.existsByCode(stockageData.getCode())) {
            throw new DuplicateCodeException("Stockage", stockageData.getCode());
        }
        
        stockage.setCode(stockageData.getCode());
        stockage.setLibelle(stockageData.getLibelle());
        
        stockage = stockageRepository.save(stockage);
        log.info("Stockage mis à jour : ID={}, Code={}", stockage.getId(), stockage.getCode());
        
        return stockage;
    }
    
    /**
     * Récupérer un stockage par ID
     */
    @Transactional(readOnly = true)
    public Stockage findById(Long id) {
        return stockageRepository.findById(id)
            .orElseThrow(() -> new StockageNotFoundException(id));
    }
    
    /**
     * Récupérer un stockage par ID (alias pour compatibilité)
     */
    @Transactional(readOnly = true)
    public Stockage getStockageById(Long id) {
        return findById(id);
    }
    
    /**
     * Récupérer un stockage par code
     */
    @Transactional(readOnly = true)
    public Stockage findByCode(String code) {
        return stockageRepository.findByCode(code)
            .orElseThrow(() -> new StockageNotFoundException("Code: " + code));
    }
    
    /**
     * Récupérer un stockage par code (optionnel)
     */
    @Transactional(readOnly = true)
    public Optional<Stockage> getStockageByCode(String code) {
        return stockageRepository.findByCode(code);
    }
    
    /**
     * Lister tous les stockages
     */
    @Transactional(readOnly = true)
    public List<Stockage> findAll() {
        return stockageRepository.findAll();
    }
    
    /**
     * Lister tous les stockages (alias)
     */
    @Transactional(readOnly = true)
    public List<Stockage> getAllStockages() {
        return findAll();
    }
    
    /**
     * Lister tous les stockages avec pagination
     */
    @Transactional(readOnly = true)
    public Page<Stockage> findAll(Pageable pageable) {
        return stockageRepository.findAll(pageable);
    }
    
    /**
     * Lister tous les stockages avec pagination (alias)
     */
    @Transactional(readOnly = true)
    public Page<Stockage> getAllStockages(Pageable pageable) {
        return findAll(pageable);
    }
    
    /**
     * Rechercher par libellé (contient, insensible à la casse)
     */
    @Transactional(readOnly = true)
    public List<Stockage> findByLibelleContaining(String libelle) {
        return stockageRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    /**
     * Rechercher par libellé (contient, insensible à la casse) - alias
     */
    @Transactional(readOnly = true)
    public List<Stockage> searchByLibelle(String libelle) {
        return findByLibelleContaining(libelle);
    }
    
    /**
     * Rechercher par code (contient, insensible à la casse)
     */
    @Transactional(readOnly = true)
    public List<Stockage> searchByCode(String code) {
        return stockageRepository.findByCodeContainingIgnoreCase(code);
    }
    
    /**
     * Rechercher multicritères
     */
    @Transactional(readOnly = true)
    public List<Stockage> rechercherStockages(String code, String libelle, String adresse) {
        return stockageRepository.findBySearchCriteria(code, libelle, Pageable.unpaged()).getContent();
    }
    
    /**
     * Rechercher par libellé ou code
     */
    @Transactional(readOnly = true)
    public Page<Stockage> searchStockages(String searchTerm, Pageable pageable) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return findAll(pageable);
        }
        return stockageRepository.findByLibelleContainingIgnoreCaseOrCodeContainingIgnoreCase(
            searchTerm, searchTerm, pageable);
    }
    
    @Transactional(readOnly = true)
    public List<Stockage> searchByCriteria(String code, String libelle) {
        return stockageRepository.findBySearchCriteria(code, libelle, Pageable.unpaged()).getContent();
    }

    /**
     * Supprimer un stockage
     */
    public void supprimerStockage(Long id) {
        Stockage stockage = findById(id);
        
        // Vérifier qu'il n'y a pas de stocks associés
        if (stockage.getStocks() != null && !stockage.getStocks().isEmpty()) {
            throw new InvalidOperationException("Stockage", 
                "Impossible de supprimer le stockage car il contient des stocks");
        }
        
        stockageRepository.deleteById(id);
        log.info("Stockage supprimé : ID={}, Code={}", id, stockage.getCode());
    }
    
    /**
     * Vérifier si un stockage existe par code
     */
    @Transactional(readOnly = true)
    public boolean existsByCode(String code) {
        return stockageRepository.existsByCode(code);
    }
    
    /**
     * Vérifier si un stockage existe par ID
     */
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return stockageRepository.existsById(id);
    }
    
    /**
     * Obtenir les stocks d'un stockage
     */
    @Transactional(readOnly = true)
    public List<Stock> getStocksOfStockage(Long stockageId) {
        Stockage stockage = findById(stockageId);
        return stockage.getStocks();
    }
    
    /**
     * Compter le nombre total de stockages
     */
    @Transactional(readOnly = true)
    public long countStockages() {
        return stockageRepository.count();
    }
    
    /**
     * Obtenir les stockages ayant des stocks
     */
    @Transactional(readOnly = true)
    public List<Stockage> getStockagesWithStocks() {
        return stockageRepository.findStockagesWithStocks();
    }
    
    /**
     * Obtenir les stockages sans stocks
     */
    @Transactional(readOnly = true)
    public List<Stockage> getStockagesSansStocks() {
        return stockageRepository.findStockagesWithoutStocks();
    }
    
    /**
     * Obtenir les stockages sans stocks (alias)
     */
    @Transactional(readOnly = true)
    public List<Stockage> getStockagesWithoutStocks() {
        return getStockagesSansStocks();
    }
    
    /**
     * Compter le nombre de stocks par stockage
     */
    @Transactional(readOnly = true)
    public long countStocksByStockage(Long stockageId) {
        return stockageRepository.countStocksByStockageId(stockageId);
    }
    
    /**
     * Supprimer en masse (batch delete)
     */
    public void supprimerStockages(List<Long> ids) {
        List<Stockage> stockages = stockageRepository.findAllById(ids);
        
        // Vérifier qu'aucun stockage n'a de stocks associés
        for (Stockage stockage : stockages) {
            if (stockage.getStocks() != null && !stockage.getStocks().isEmpty()) {
                throw new InvalidOperationException("Stockage", 
                    String.format("Impossible de supprimer le stockage %s car il contient des stocks", 
                    stockage.getCode()));
            }
        }
        
        stockageRepository.deleteAllById(ids);
        log.info("Suppression en masse de {} stockages", ids.size());
    }
    
    /**
     * Activer/Désactiver un stockage (si vous ajoutez un champ actif)
     */
    public Stockage changerStatutStockage(Long id, boolean actif) {
        Stockage stockage = findById(id);
        // Note: Vous devrez ajouter un champ 'actif' dans l'entité Stockage si nécessaire
        // stockage.setActif(actif);
        return stockageRepository.save(stockage);
    }
} 