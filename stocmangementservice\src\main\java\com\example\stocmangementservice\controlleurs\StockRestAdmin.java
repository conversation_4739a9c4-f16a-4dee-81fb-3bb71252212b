package com.example.stocmangementservice.controlleurs;

import com.example.stocmangementservice.model.Stock;
import com.example.stocmangementservice.dtos.StockDto;
import com.example.stocmangementservice.services.MapperService;
import com.example.stocmangementservice.services.StockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/stock/")
@Tag(name = "Stock", description = "API de gestion des stocks")
public class StockRestAdmin {

    private final StockService service;
    private final MapperService mapper;

    public StockRestAdmin(StockService service, MapperService mapper) {
        this.service = service;
        this.mapper = mapper;
    }

    @Operation(summary = "Finds a list of all stocks")
    @GetMapping("")
    public ResponseEntity<List<Stock>> findAll() {
        ResponseEntity<List<Stock>> res;
        List<Stock> list = service.findAll();
        HttpStatus status = HttpStatus.NO_CONTENT;
        if (list != null && !list.isEmpty())
            status = HttpStatus.OK;
        res = new ResponseEntity<>(list, status);
        return res;
    }

    @Operation(summary = "Finds an optimized list of all stocks")
    @GetMapping("optimized")
    public ResponseEntity<List<Stock>> findAllOptimized() {
        ResponseEntity<List<Stock>> res;
        List<Stock> list = service.findAllOrderByLibelle();
        HttpStatus status = HttpStatus.NO_CONTENT;
        if (list != null && !list.isEmpty())
            status = HttpStatus.OK;
        res = new ResponseEntity<>(list, status);
        return res;
    }

    @Operation(summary = "Finds a stock by id")
    @GetMapping("id/{id}")
    public ResponseEntity<Stock> findById(@PathVariable Long id) {
        Stock t = service.findById(id);
        if (t != null) {
            return getDtoResponseEntity(t);
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Finds a stock by code")
    @GetMapping("code/{code}")
    public ResponseEntity<Stock> findByCode(@PathVariable String code) {
        Stock t = service.findByCode(code);
        if (t != null) {
            return getDtoResponseEntity(t);
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(summary = "Finds stocks by libelle")
    @GetMapping("libelle/{libelle}")
    public ResponseEntity<List<Stock>> findByLibelle(@PathVariable String libelle) {
        List<Stock> list = service.findByLibelleContaining(libelle);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Saves the specified stock")
    @PostMapping("")
    public ResponseEntity<Stock> save(@RequestBody StockDto dto) {
        if (dto == null) {
            return ResponseEntity.noContent().build();
        }
        
        Stock t = service.creerStock(dto);
        
        if (t == null) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } else {
            return ResponseEntity.status(HttpStatus.CREATED).body(t);
        }
    }

    @Operation(summary = "Updates the specified stock")
    @PutMapping("")
    public ResponseEntity<Stock> update(@RequestBody StockDto dto) {
        ResponseEntity<Stock> res;
        if (dto.getId() == null || service.findById(dto.getId()) == null)
            res = new ResponseEntity<>(HttpStatus.CONFLICT);
        else {
            Stock updated = service.modifierStock(dto.getId(), dto);
            res = new ResponseEntity<>(updated, HttpStatus.OK);
        }
        return res;
    }

    @Operation(summary = "Delete list of stock")
    @PostMapping("multiple")
    public ResponseEntity<List<Long>> delete(@RequestBody List<StockDto> dtos) {
        ResponseEntity<List<Long>> res;
        HttpStatus status = HttpStatus.CONFLICT;
        List<Long> ids = new java.util.ArrayList<>();
        
        if (dtos != null && !dtos.isEmpty()) {
            for (StockDto dto : dtos) {
                if (dto.getId() != null) {
                    service.supprimerStock(dto.getId());
                    ids.add(dto.getId());
                }
            }
            status = HttpStatus.OK;
        }
        res = new ResponseEntity<>(ids, status);
        return res;
    }

    @Operation(summary = "Delete the specified stock")
    @DeleteMapping("id/{id}")
    public ResponseEntity<Long> deleteById(@PathVariable Long id) {
        ResponseEntity<Long> res;
        HttpStatus status = HttpStatus.PRECONDITION_FAILED;
        if (id != null) {
            try {
                service.supprimerStock(id);
                status = HttpStatus.OK;
            } catch (Exception e) {
                status = HttpStatus.CONFLICT;
            }
        }
        res = new ResponseEntity<>(id, status);
        return res;
    }

    @Operation(summary = "Search stocks by criteria")
    @GetMapping("search")
    public ResponseEntity<List<Stock>> search(
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String libelle,
            @RequestParam(required = false) Long stockageId) {
        
        List<Stock> list = service.rechercherStocks(code, libelle, stockageId);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stocks by stockage")
    @GetMapping("stockage/{stockageId}")
    public ResponseEntity<List<Stock>> getStocksByStockage(@PathVariable Long stockageId) {
        List<Stock> list = service.findByStockageId(stockageId);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stocks by stockage code")
    @GetMapping("stockage/code/{code}")
    public ResponseEntity<List<Stock>> getStocksByStockageCode(@PathVariable String code) {
        List<Stock> list = service.findByStockageCode(code);
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stocks with products")
    @GetMapping("with-products")
    public ResponseEntity<List<Stock>> getStocksWithProducts() {
        List<Stock> list = service.getStocksWithProducts();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Get stocks without products")
    @GetMapping("without-products")
    public ResponseEntity<List<Stock>> getStocksWithoutProducts() {
        List<Stock> list = service.getStocksSansProducts();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Count products by stock")
    @GetMapping("count-products")
    public ResponseEntity<List<Object[]>> countProductsByStock() {
        List<Object[]> list = service.countProductsByStock();
        HttpStatus status = list != null && !list.isEmpty() ? HttpStatus.OK : HttpStatus.NO_CONTENT;
        return new ResponseEntity<>(list, status);
    }

    @Operation(summary = "Check if stock exists by code")
    @GetMapping("exists/{code}")
    public ResponseEntity<Boolean> existsByCode(@PathVariable String code) {
        boolean exists = service.existsByCode(code);
        return ResponseEntity.ok(exists);
    }

    private ResponseEntity<Stock> getDtoResponseEntity(Stock stock) {
        return new ResponseEntity<>(stock, HttpStatus.OK);
    }
} 