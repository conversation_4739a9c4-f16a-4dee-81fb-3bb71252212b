<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-core:5.17.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.mockito" artifactId="mockito-core" version="5.17.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/5.17.0/f5fe5a2f94eb65f4f83ca0607bfe13ec0d9c6f3b/mockito-core-5.17.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/5.17.0/fccd4848a20c5cb3abd8bb066d7f0253985750b6/mockito-core-5.17.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>