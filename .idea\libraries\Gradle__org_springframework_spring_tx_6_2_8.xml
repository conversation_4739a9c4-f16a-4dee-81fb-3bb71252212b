<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-tx:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-tx" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-tx/6.2.8/6b2eecf34f0810dd989f7e7bfa8fd8c86fe28dfb/spring-tx-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-tx/6.2.8/40ef975920e701284c2bc3c1ead2b35798536216/spring-tx-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>