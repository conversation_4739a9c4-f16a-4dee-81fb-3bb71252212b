<component name="libraryTable">
  <library name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.19.1" type="java-imported" external-system-id="Maven">
    <properties groupId="com.fasterxml.jackson.core" artifactId="jackson-annotations" version="2.19.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>