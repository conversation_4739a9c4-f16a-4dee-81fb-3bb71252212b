<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-logging:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-logging" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/3.5.3/7013c1aff95fd599eb291fe34924c59d1ea88c5b/spring-boot-starter-logging-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/3.5.3/5fe9f3627c54dc338e9878c457555091b9ee1590/spring-boot-starter-logging-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>