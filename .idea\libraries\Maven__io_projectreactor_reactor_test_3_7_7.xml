<component name="libraryTable">
  <library name="Maven: io.projectreactor:reactor-test:3.7.7" type="java-imported" external-system-id="Maven">
    <properties groupId="io.projectreactor" artifactId="reactor-test" version="3.7.7" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/reactor-test/3.7.7/reactor-test-3.7.7.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/reactor-test/3.7.7/reactor-test-3.7.7-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/reactor-test/3.7.7/reactor-test-3.7.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>