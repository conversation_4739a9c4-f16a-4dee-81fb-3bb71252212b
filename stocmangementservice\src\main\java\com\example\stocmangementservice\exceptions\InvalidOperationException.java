package com.example.stocmangementservice.exceptions;

/**
 * Exception levée quand une opération métier n'est pas autorisée
 */
public class InvalidOperationException extends StockException {
    
    public InvalidOperationException(String operation, String reason) {
        super(String.format("Opération '%s' non autorisée: %s", operation, reason));
    }
    
    public InvalidOperationException(String message) {
        super(message);
    }
    
    public InvalidOperationException(String message, Throwable cause) {
        super(message, cause);
    }
} 