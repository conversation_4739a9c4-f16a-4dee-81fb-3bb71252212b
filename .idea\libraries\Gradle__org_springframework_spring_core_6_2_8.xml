<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-core:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-core" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-core/6.2.8/2caf1cef93252f5ef2b7f334b8b4d61f3aecad15/spring-core-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-core/6.2.8/7428698e25e731a7357607d5122dc2a4b100252a/spring-core-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>