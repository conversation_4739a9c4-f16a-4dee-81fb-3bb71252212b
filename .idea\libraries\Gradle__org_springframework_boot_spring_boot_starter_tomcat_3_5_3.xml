<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-tomcat:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-tomcat" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-tomcat/3.5.3/490bccd84af19bddcb955e722c8dcebc34cb2193/spring-boot-starter-tomcat-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-tomcat/3.5.3/bf014323c960acf16cbad203014d4dbaeb193755/spring-boot-starter-tomcat-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>