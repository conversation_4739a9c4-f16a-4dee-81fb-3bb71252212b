<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.inject:jersey-hk2:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.inject" artifactId="jersey-hk2" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.inject/jersey-hk2/3.1.10/85fa449300069937189bf0cfeb6da765e5e3854a/jersey-hk2-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.inject/jersey-hk2/3.1.10/b705e99750931e294eaf435c8ab780713b78337a/jersey-hk2-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>