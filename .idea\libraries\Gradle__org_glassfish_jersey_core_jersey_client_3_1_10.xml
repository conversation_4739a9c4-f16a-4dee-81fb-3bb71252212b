<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.core:jersey-client:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.core" artifactId="jersey-client" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-client/3.1.10/35d87d5a4af28c80e380a00efba94d9af8998849/jersey-client-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.core/jersey-client/3.1.10/32e704a971a4611be08fe71424cad0675ac2e7b3/jersey-client-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>