package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.ProductMarchand;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository simple pour l'entité ProductMarchand - CRUD de base
 */
@Repository
public interface ProductMarchandRepository extends JpaRepository<ProductMarchand, Long> {
    
    // Recherche par code
    Optional<ProductMarchand> findByCode(String code);
    
    // Recherche par libellé
    List<ProductMarchand> findByLibelleContainingIgnoreCase(String libelle);
    
    // Vérifier l'existence par code
    boolean existsByCode(String code);
    
    // Produits ordonnés par libellé
    List<ProductMarchand> findAllByOrderByLibelleAsc();
}
