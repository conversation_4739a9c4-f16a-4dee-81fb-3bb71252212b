<component name="libraryTable">
  <library name="Gradle: org.springframework.security:spring-security-oauth2-resource-server:6.5.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.security" artifactId="spring-security-oauth2-resource-server" version="6.5.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-resource-server/6.5.1/b580e04150547293ac01041f37f7b23415aefdf3/spring-security-oauth2-resource-server-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.security/spring-security-oauth2-resource-server/6.5.1/1ec31bae1a351ca900887d1d53f43d30dd7b66f7/spring-security-oauth2-resource-server-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>