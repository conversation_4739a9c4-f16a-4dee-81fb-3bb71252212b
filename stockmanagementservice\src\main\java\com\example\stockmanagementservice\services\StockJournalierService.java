package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.StockJournalierDto;
import com.example.stockmanagementservice.model.StockJournalier;
import com.example.stockmanagementservice.repositories.StockJournalierRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des stocks journaliers - CRUD de base
 */
@Service
public class StockJournalierService {
    
    @Autowired
    private StockJournalierRepository stockJournalierRepository;
    
    // Créer un stock journalier
    public StockJournalier createStockJournalier(StockJournalier stockJournalier) {
        return stockJournalierRepository.save(stockJournalier);
    }
    
    // Récupérer tous les stocks journaliers
    public List<StockJournalier> getAllStockJournaliers() {
        return stockJournalierRepository.findAll();
    }
    
    // Récupérer un stock journalier par ID
    public Optional<StockJournalier> getStockJournalierById(Long id) {
        return stockJournalierRepository.findById(id);
    }
    
    // Récupérer les stocks journaliers par date
    public List<StockJournalier> getStockJournaliersByDate(LocalDate date) {
        return stockJournalierRepository.findByDate(date);
    }
    
    // Récupérer les stocks journaliers par période
    public List<StockJournalier> getStockJournaliersByPeriod(LocalDate startDate, LocalDate endDate) {
        return stockJournalierRepository.findByDateBetween(startDate, endDate);
    }
    
    // Récupérer les stocks journaliers par stock
    public List<StockJournalier> getStockJournaliersByStock(Long stockId) {
        return stockJournalierRepository.findByStockId(stockId);
    }
    
    // Récupérer les stocks journaliers par produit
    public List<StockJournalier> getStockJournaliersByProduit(Long produitId) {
        return stockJournalierRepository.findByProduitId(produitId);
    }
    
    // Récupérer les stocks journaliers par stock et date
    public List<StockJournalier> getStockJournaliersByStockAndDate(Long stockId, LocalDate date) {
        return stockJournalierRepository.findByStockIdAndDate(stockId, date);
    }
    
    // Mettre à jour un stock journalier
    public StockJournalier updateStockJournalier(Long id, StockJournalier stockJournalierDetails) {
        Optional<StockJournalier> optionalStockJournalier = stockJournalierRepository.findById(id);
        if (optionalStockJournalier.isPresent()) {
            StockJournalier stockJournalier = optionalStockJournalier.get();
            stockJournalier.setJour(stockJournalierDetails.getJour());
            stockJournalier.setQuantite(stockJournalierDetails.getQuantite());
            return stockJournalierRepository.save(stockJournalier);
        }
        return null;
    }
    
    // Supprimer un stock journalier
    public boolean deleteStockJournalier(Long id) {
        if (stockJournalierRepository.existsById(id)) {
            stockJournalierRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Stocks journaliers ordonnés par date
    public List<StockJournalier> getStockJournaliersOrderedByDate() {
        return stockJournalierRepository.findAllByOrderByDateDesc();
    }
    
    // Convertir entité vers DTO
    public StockJournalierDto convertToDto(StockJournalier stockJournalier) {
        return new StockJournalierDto(
            stockJournalier.getId(),
            stockJournalier.getJour(),
            stockJournalier.getStock().getId(),
            stockJournalier.getProduit().getId(),
            stockJournalier.getQuantite().intValue() // Conversion BigDecimal vers Integer
        );
    }
    
    // Convertir DTO vers entité (nécessite les entités Stock et Produit)
    public StockJournalier convertToEntity(StockJournalierDto dto) {
        StockJournalier stockJournalier = new StockJournalier();
        stockJournalier.setId(dto.getId());
        stockJournalier.setJour(dto.getDate());
        stockJournalier.setQuantite(java.math.BigDecimal.valueOf(dto.getQuantite())); // Conversion Integer vers BigDecimal
        // Note: Les entités Stock et Produit doivent être définies séparément
        return stockJournalier;
    }
}
