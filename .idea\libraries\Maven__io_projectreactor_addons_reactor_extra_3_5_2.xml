<component name="libraryTable">
  <library name="Maven: io.projectreactor.addons:reactor-extra:3.5.2" type="java-imported" external-system-id="Maven">
    <properties groupId="io.projectreactor.addons" artifactId="reactor-extra" version="3.5.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/addons/reactor-extra/3.5.2/reactor-extra-3.5.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/addons/reactor-extra/3.5.2/reactor-extra-3.5.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/addons/reactor-extra/3.5.2/reactor-extra-3.5.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>