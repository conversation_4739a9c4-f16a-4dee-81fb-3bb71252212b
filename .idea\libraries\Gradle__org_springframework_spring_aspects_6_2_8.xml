<component name="libraryTable">
  <library name="Gradle: org.springframework:spring-aspects:6.2.8" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework" artifactId="spring-aspects" version="6.2.8" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-aspects/6.2.8/30cd83452beef146187bee60a824fe3ae7197d85/spring-aspects-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework/spring-aspects/6.2.8/e269c2e4d83d332e450c7d25097bd10cef038fb2/spring-aspects-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>