<component name="libraryTable">
  <library name="Maven: com.netflix.netflix-commons:netflix-infix:0.3.0" type="java-imported" external-system-id="Maven">
    <properties groupId="com.netflix.netflix-commons" artifactId="netflix-infix" version="0.3.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>