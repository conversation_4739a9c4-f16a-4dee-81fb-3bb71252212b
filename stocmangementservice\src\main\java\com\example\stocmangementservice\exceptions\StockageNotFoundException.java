package com.example.stocmangementservice.exceptions;

/**
 * Exception levée quand un lieu de stockage n'est pas trouvé
 */
public class StockageNotFoundException extends StockException {
    
    public StockageNotFoundException(Long id) {
        super("Lieu de stockage avec l'ID " + id + " non trouvé");
    }
    
    public StockageNotFoundException(String code) {
        super("Lieu de stockage avec le code " + code + " non trouvé");
    }
    
    public StockageNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
} 