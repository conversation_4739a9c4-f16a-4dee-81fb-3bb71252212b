<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-reactor-netty:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-reactor-netty" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-reactor-netty/3.5.3/b92d9c79b1102666a2d698f5d9440ee91702f2fe/spring-boot-starter-reactor-netty-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-reactor-netty/3.5.3/f57e283deeeb98525f69b795919c464b2c236c2/spring-boot-starter-reactor-netty-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>