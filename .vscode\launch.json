{"configurations": [{"type": "java", "name": "Spring Boot-EurekaServerApplication<eureka-server>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.example.eurekaserver.EurekaServerApplication", "projectName": "eureka-server", "args": "", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "Spring Boot-ApiGatewayApplication<api-gateway>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.example.apigateway.ApiGatewayApplication", "projectName": "api-gateway", "args": "", "envFile": "${workspaceFolder}/.env"}]}