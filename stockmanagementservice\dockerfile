# Stage 1: Build the jar using Maven
FROM maven:3.9.4-eclipse-temurin-21 AS build
WORKDIR /app

COPY mvnw .
COPY .mvn .mvn
COPY pom.xml .
COPY src src

RUN chmod +x ./mvnw
RUN ./mvnw clean package -DskipTests

# Stage 2: Run the jar with JDK 21
FROM eclipse-temurin:21-jdk-alpine
WORKDIR /app

COPY --from=build /app/target/stockmanagementservice-0.0.1-SNAPSHOT.jar stock-management-service.jar

EXPOSE 8081

ENTRYPOINT ["java", "-jar", "stock-management-service.jar"]
