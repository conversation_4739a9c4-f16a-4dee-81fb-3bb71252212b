package com.example.stocmangementservice.dtos;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO pour les stocks journaliers
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockJournalierDto {
    
    private Long id;
    
    private String code;
    
    private String libelle;
    
    private String description;
    
    // Quantité totale
    @PositiveOrZero(message = "La quantité doit être supérieure ou égale à 0")
    private BigDecimal quantite = BigDecimal.ZERO;
    
    @NotNull(message = "La date est obligatoire")
    private LocalDate jour;
    
    private Boolean expedition;
    
    @NotNull(message = "L'ID du stock-produit est obligatoire")
    private Long stockProduitId;
    
    // Informations du stock (pour les vues)
    private String stockCode;
    private String stockLibelle;
    
    // Informations du produit (pour les vues)
    private String produitCode;
    private String produitLibelle;
    
    // Informations du stockage (pour les vues)
    private String stockageCode;
    private String stockageLibelle;
} 