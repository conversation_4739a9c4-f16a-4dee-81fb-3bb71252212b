<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-actuator:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-actuator" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/3.5.3/981f7aa6c9a8244f88e41bd8564288657713c67d/spring-boot-starter-actuator-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/3.5.3/9c5f70db878b75ab89eebcf2f79ebe59adf143f1/spring-boot-starter-actuator-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>