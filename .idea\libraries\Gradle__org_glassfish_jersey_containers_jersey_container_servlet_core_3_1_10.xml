<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.containers" artifactId="jersey-container-servlet-core" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.containers/jersey-container-servlet-core/3.1.10/a2106c310bfc6847e5ed5ca85b306645ff6a0c66/jersey-container-servlet-core-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.containers/jersey-container-servlet-core/3.1.10/8be524334675c38591f7e115dafc7879c8b770a8/jersey-container-servlet-core-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>