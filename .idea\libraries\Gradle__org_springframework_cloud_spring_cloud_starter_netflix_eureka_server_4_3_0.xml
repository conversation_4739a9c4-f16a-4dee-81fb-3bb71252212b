<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-starter-netflix-eureka-server:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-netflix-eureka-server" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-starter-netflix-eureka-server/4.3.0/9a1e101ae51c51b8c3cd2a90e22259112de4bf04/spring-cloud-starter-netflix-eureka-server-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>