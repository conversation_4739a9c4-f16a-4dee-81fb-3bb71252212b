spring:
  application:
    name: STOCK-MANAGEMENT-SERVICE
  
  datasource:
    url: ****************************************
    username: localhost
    password: 1234
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/

server:
  port: 8081 