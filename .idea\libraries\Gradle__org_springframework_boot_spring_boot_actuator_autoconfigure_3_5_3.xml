<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-actuator-autoconfigure:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-actuator-autoconfigure" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator-autoconfigure/3.5.3/599370a392f858694a2ce3bcf2c29dac818760a6/spring-boot-actuator-autoconfigure-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator-autoconfigure/3.5.3/301eec97dbb1215b7bb3b0200af5f69c8b0b6b53/spring-boot-actuator-autoconfigure-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>