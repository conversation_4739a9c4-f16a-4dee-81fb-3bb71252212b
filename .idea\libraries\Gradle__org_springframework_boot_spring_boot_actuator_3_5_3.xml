<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-actuator:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-actuator" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator/3.5.3/84a318d937ebf3d4f56639d712deb2054a78d74/spring-boot-actuator-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-actuator/3.5.3/db7f9a458374ebd7c79506be80402a81278b96c8/spring-boot-actuator-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>