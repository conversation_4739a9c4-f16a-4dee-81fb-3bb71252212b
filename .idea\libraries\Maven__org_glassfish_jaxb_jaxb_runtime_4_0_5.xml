<component name="libraryTable">
  <library name="Maven: org.glassfish.jaxb:jaxb-runtime:4.0.5" type="java-imported" external-system-id="Maven">
    <properties groupId="org.glassfish.jaxb" artifactId="jaxb-runtime" version="4.0.5" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>