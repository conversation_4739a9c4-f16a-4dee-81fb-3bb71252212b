<component name="libraryTable">
  <library name="Gradle: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.fasterxml.jackson.datatype" artifactId="jackson-datatype-jsr310" version="2.19.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jsr310/2.19.1/e9df57105f3b0730040ad7f3a1b603684a3329f4/jackson-datatype-jsr310-2.19.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-jsr310/2.19.1/67b5bee91e5c0a75247f451d0e795a9a54cc45ed/jackson-datatype-jsr310-2.19.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>