<component name="libraryTable">
  <library name="Gradle: org.springframework.boot:spring-boot-starter-validation:3.5.3" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.boot" artifactId="spring-boot-starter-validation" version="3.5.3" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-validation/3.5.3/aaa11ca06f6701717dd7f07666079285e1a566bb/spring-boot-starter-validation-3.5.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-validation/3.5.3/500a88dc30454b28cbcf7f58615bc22a105ab819/spring-boot-starter-validation-3.5.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>