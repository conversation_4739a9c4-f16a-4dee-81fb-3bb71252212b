<component name="libraryTable">
  <library name="Gradle: io.netty:netty-resolver-dns-native-macos:osx-x86_64:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-resolver-dns-native-macos" version="4.1.122.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-native-macos/4.1.122.Final/71de9ad9975e7d5d3b0490a89d5d00e66000403c/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-native-macos/4.1.122.Final/a169615ea365c70dcaa9af6ae6ef53969f8c846d/netty-resolver-dns-native-macos-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>