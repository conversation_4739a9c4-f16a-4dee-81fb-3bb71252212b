<component name="libraryTable">
  <library name="Maven: org.antlr:antlr4-runtime:4.13.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.antlr" artifactId="antlr4-runtime" version="4.13.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>