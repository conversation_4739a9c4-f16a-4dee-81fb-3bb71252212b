<component name="libraryTable">
  <library name="Gradle: com.netflix.netflix-commons:netflix-eventbus:0.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="com.netflix.netflix-commons" artifactId="netflix-eventbus" version="0.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.netflix.netflix-commons/netflix-eventbus/0.3.0/3f864adbe81f0849729fcbba3fe693c32be739ea/netflix-eventbus-0.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.netflix.netflix-commons/netflix-eventbus/0.3.0/a942ee54e012e3f73bc940a94e4d4982b7917729/netflix-eventbus-0.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>