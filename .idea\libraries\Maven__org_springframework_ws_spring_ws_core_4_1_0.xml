<component name="libraryTable">
  <library name="Maven: org.springframework.ws:spring-ws-core:4.1.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework.ws" artifactId="spring-ws-core" version="4.1.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/ws/spring-ws-core/4.1.0/spring-ws-core-4.1.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/ws/spring-ws-core/4.1.0/spring-ws-core-4.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/ws/spring-ws-core/4.1.0/spring-ws-core-4.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>