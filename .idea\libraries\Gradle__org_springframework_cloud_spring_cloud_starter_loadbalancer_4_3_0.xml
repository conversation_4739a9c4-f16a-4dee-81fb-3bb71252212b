<component name="libraryTable">
  <library name="Gradle: org.springframework.cloud:spring-cloud-starter-loadbalancer:4.3.0" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-starter-loadbalancer" version="4.3.0" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.cloud/spring-cloud-starter-loadbalancer/4.3.0/4567330739fc9c8d87dac64a2775e77343423d9f/spring-cloud-starter-loadbalancer-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>