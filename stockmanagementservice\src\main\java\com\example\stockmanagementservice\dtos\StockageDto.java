package com.example.stockmanagementservice.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO pour les lieux de stockage
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockageDto {
    
    private Long id;
    
    @NotBlank(message = "Le code est obligatoire")
    @Size(max = 255, message = "Le code ne peut pas dépasser 255 caractères")
    private String code;
    
    @NotBlank(message = "Le libellé est obligatoire")
    @Size(max = 255, message = "Le libellé ne peut pas dépasser 255 caractères")
    private String libelle;
    
    // Liste des stocks associés (optionnel, pour les vues détaillées)
    private List<StockDto> stocks;
} 
