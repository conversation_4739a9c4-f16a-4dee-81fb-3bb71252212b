<component name="libraryTable">
  <library name="Maven: org.junit.platform:junit-platform-commons:1.12.2" type="java-imported" external-system-id="Maven">
    <properties groupId="org.junit.platform" artifactId="junit-platform-commons" version="1.12.2" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>