<component name="libraryTable">
  <library name="Gradle: org.glassfish.jersey.containers:jersey-container-servlet:3.1.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.glassfish.jersey.containers" artifactId="jersey-container-servlet" version="3.1.10" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.containers/jersey-container-servlet/3.1.10/37af4f4263933a4b3aa9334fdba3c6385e0c9ce8/jersey-container-servlet-3.1.10.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.glassfish.jersey.containers/jersey-container-servlet/3.1.10/7ae125df8074c8708ab19793679748fb9a7b15fa/jersey-container-servlet-3.1.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>