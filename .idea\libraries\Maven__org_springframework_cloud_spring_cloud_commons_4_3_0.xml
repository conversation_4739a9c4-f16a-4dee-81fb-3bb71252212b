<component name="libraryTable">
  <library name="Maven: org.springframework.cloud:spring-cloud-commons:4.3.0" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework.cloud" artifactId="spring-cloud-commons" version="4.3.0" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/cloud/spring-cloud-commons/4.3.0/spring-cloud-commons-4.3.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/cloud/spring-cloud-commons/4.3.0/spring-cloud-commons-4.3.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/cloud/spring-cloud-commons/4.3.0/spring-cloud-commons-4.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>