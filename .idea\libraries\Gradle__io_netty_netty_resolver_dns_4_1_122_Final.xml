<component name="libraryTable">
  <library name="Gradle: io.netty:netty-resolver-dns:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-resolver-dns" version="4.1.122.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns/4.1.122.Final/42d8f33f4ba93d0be171b21c59af48b8701f5a93/netty-resolver-dns-4.1.122.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver-dns/4.1.122.Final/31f723967abf2f9703e3d16c00999d7a9b5e181d/netty-resolver-dns-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>