package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.Produit;
import com.example.stockmanagementservice.model.TypeProduit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité Produit (classe abstraite)
 * Gère ProductMarchand et ProductSource via l'héritage SINGLE_TABLE
 */
@Repository
public interface ProduitRepository extends JpaRepository<Produit, Long> {
    
    /**
     * Recherche par code (unique)
     */
    Optional<Produit> findByCode(String code);
    
    /**
     * Vérifier l'existence par code
     */
    boolean existsByCode(String code);
    
    /**
     * Recherche par type de produit (MARCHAND/SOURCE) via discriminator
     */
    @Query("SELECT p FROM Produit p WHERE TYPE(p) = :type")
    List<Produit> findByType(@Param("type") Class<? extends Produit> type);
    
    /**
     * Recherche par libellé (contient, insensible à la casse)
     */
    List<Produit> findByLibelleContainingIgnoreCase(String libelle);
    
    /**
     * Recherche multicritères avec pagination
     */
    @Query("SELECT p FROM Produit p WHERE " +
           "(:code IS NULL OR LOWER(p.code) LIKE LOWER(CONCAT('%', :code, '%'))) AND " +
           "(:libelle IS NULL OR LOWER(p.libelle) LIKE LOWER(CONCAT('%', :libelle, '%'))) AND " +
           "(:typeProduit IS NULL OR TYPE(p) = :typeProduit) AND " +
           "(:style IS NULL OR LOWER(p.style) LIKE LOWER(CONCAT('%', :style, '%')))")
    Page<Produit> findBySearchCriteria(
        @Param("code") String code,
        @Param("libelle") String libelle, 
        @Param("typeProduit") Class<? extends Produit> typeProduit,
        @Param("style") String style,
        Pageable pageable
    );
    
    /**
     * Recherche par attributs booléens
     */
    List<Produit> findByDefaultReclamationTrue();
    List<Produit> findByExportTrue();
    
    /**
     * Compter les produits par type
     */
    @Query("SELECT COUNT(p) FROM Produit p WHERE TYPE(p) = :type")
    Long countByType(@Param("type") Class<? extends Produit> type);
    
    /**
     * Recherche des produits ayant des stocks
     */
    @Query("SELECT DISTINCT p FROM Produit p JOIN p.stockProduits sp")
    List<Produit> findProduitsWithStock();
    
    /**
     * Recherche des produits sans stock
     */
    @Query("SELECT p FROM Produit p WHERE p.stockProduits IS EMPTY")
    List<Produit> findProduitsSansStock();
} 
