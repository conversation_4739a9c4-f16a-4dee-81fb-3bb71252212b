package com.example.stockmanagementservice.repositories;

import com.example.stockmanagementservice.model.Produit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository simple pour l'entité Produit - CRUD de base
 */
@Repository
public interface ProduitRepository extends JpaRepository<Produit, Long> {

    // Recherche par code
    Optional<Produit> findByCode(String code);

    // Recherche par libellé
    List<Produit> findByLibelleContainingIgnoreCase(String libelle);

    // Vérifier l'existence par code
    boolean existsByCode(String code);
} 
