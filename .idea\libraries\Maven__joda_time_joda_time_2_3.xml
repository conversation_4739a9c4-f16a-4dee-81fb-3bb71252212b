<component name="libraryTable">
  <library name="Maven: joda-time:joda-time:2.3" type="java-imported" external-system-id="Maven">
    <properties groupId="joda-time" artifactId="joda-time" version="2.3" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/joda-time/joda-time/2.3/joda-time-2.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/joda-time/joda-time/2.3/joda-time-2.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/joda-time/joda-time/2.3/joda-time-2.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>