package com.example.stockmanagementservice.controlleurs;

import com.example.stockmanagementservice.dtos.StockageDto;
import com.example.stockmanagementservice.model.Stockage;
import com.example.stockmanagementservice.services.MapperService;
import com.example.stockmanagementservice.services.StockageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/stockages")
@Tag(name = "Stockage", description = "API de gestion des stockages")
public class StockageRestAdmin {

    private final StockageService service;
    private final MapperService mapper;

    public StockageRestAdmin(StockageService service, MapperService mapper) {
        this.service = service;
        this.mapper = mapper;
    }

    @Operation(summary = "Lister tous les stockages")
    @GetMapping
    public ResponseEntity<List<StockageDto>> findAll() {
        List<StockageDto> dtos = service.getAllStockages()
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    @Operation(summary = "Rechercher un stockage par ID")
    @GetMapping("/{id}")
    public ResponseEntity<StockageDto> findById(@PathVariable Long id) {
        Stockage stockage = service.getStockageById(id);
        return ResponseEntity.ok(mapper.toDto(stockage));
    }

    @Operation(summary = "Rechercher un stockage par code")
    @GetMapping("/code/{code}")
    public ResponseEntity<StockageDto> findByCode(@PathVariable String code) {
        return service.getStockageByCode(code)
                .map(stockage -> ResponseEntity.ok(mapper.toDto(stockage)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "Rechercher les stockages par libellé (contenant)")
    @GetMapping("/libelle/{libelle}")
    public ResponseEntity<List<StockageDto>> findByLibelle(@PathVariable String libelle) {
        List<StockageDto> dtos = service.searchByLibelle(libelle)
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    @Operation(summary = "Créer un nouveau stockage")
    @PostMapping
    public ResponseEntity<StockageDto> save(@Valid @RequestBody StockageDto dto) {
        Stockage stockage = service.creerStockage(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(mapper.toDto(stockage));
    }

    @Operation(summary = "Mettre à jour un stockage existant")
    @PutMapping("/{id}")
    public ResponseEntity<StockageDto> update(@PathVariable Long id, @Valid @RequestBody StockageDto dto) {
        Stockage updated = service.mettreAJourStockage(id, dto);
        return ResponseEntity.ok(mapper.toDto(updated));
    }

    @Operation(summary = "Supprimer un stockage par ID")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteById(@PathVariable Long id) {
        service.supprimerStockage(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Supprimer plusieurs stockages par liste d'IDs")
    @PostMapping("/delete-multiple")
    public ResponseEntity<Void> deleteMultiple(@RequestBody List<Long> ids) {
        service.supprimerStockages(ids);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Lister les stockages contenant des stocks")
    @GetMapping("/with-stocks")
    public ResponseEntity<List<StockageDto>> getStockagesWithStocks() {
        List<StockageDto> dtos = service.getStockagesWithStocks()
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    @Operation(summary = "Lister les stockages sans stocks")
    @GetMapping("/without-stocks")
    public ResponseEntity<List<StockageDto>> getStockagesWithoutStocks() {
        List<StockageDto> dtos = service.getStockagesWithoutStocks()
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    @Operation(summary = "Rechercher des stockages par code ou libelle (filtrage simple)")
    @GetMapping("/search")
    public ResponseEntity<List<StockageDto>> search(
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String libelle
    ) {
        List<StockageDto> dtos = service.searchByCriteria(code, libelle)
                .stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }
}
