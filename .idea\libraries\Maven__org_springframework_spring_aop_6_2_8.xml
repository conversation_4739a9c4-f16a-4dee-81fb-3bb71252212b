<component name="libraryTable">
  <library name="Maven: org.springframework:spring-aop:6.2.8" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework" artifactId="spring-aop" version="6.2.8" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8-sources.jar!/" />
    </SOURCES>
  </library>
</component>