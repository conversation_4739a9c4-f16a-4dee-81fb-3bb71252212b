package com.example.stockmanagementservice.services;

import com.example.stockmanagementservice.dtos.ProductSourceDto;
import com.example.stockmanagementservice.model.ProductSource;
import com.example.stockmanagementservice.repositories.ProductSourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service simple pour la gestion des produits sources - CRUD de base
 */
@Service
public class ProductSourceService {
    
    @Autowired
    private ProductSourceRepository productSourceRepository;
    
    // Créer un produit source
    public ProductSource createProductSource(ProductSource productSource) {
        return productSourceRepository.save(productSource);
    }
    
    // Récupérer tous les produits sources
    public List<ProductSource> getAllProductSources() {
        return productSourceRepository.findAll();
    }
    
    // Récupérer un produit source par ID
    public Optional<ProductSource> getProductSourceById(Long id) {
        return productSourceRepository.findById(id);
    }
    
    // Récupérer un produit source par code
    public Optional<ProductSource> getProductSourceByCode(String code) {
        return productSourceRepository.findByCode(code);
    }
    
    // Rechercher par libellé
    public List<ProductSource> searchByLibelle(String libelle) {
        return productSourceRepository.findByLibelleContainingIgnoreCase(libelle);
    }
    
    // Mettre à jour un produit source
    public ProductSource updateProductSource(Long id, ProductSource productSourceDetails) {
        Optional<ProductSource> optionalProductSource = productSourceRepository.findById(id);
        if (optionalProductSource.isPresent()) {
            ProductSource productSource = optionalProductSource.get();
            productSource.setCode(productSourceDetails.getCode());
            productSource.setLibelle(productSourceDetails.getLibelle());
            productSource.setDescription(productSourceDetails.getDescription());
            return productSourceRepository.save(productSource);
        }
        return null;
    }
    
    // Supprimer un produit source
    public boolean deleteProductSource(Long id) {
        if (productSourceRepository.existsById(id)) {
            productSourceRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Produits ordonnés par libellé
    public List<ProductSource> getProductSourcesOrderedByLibelle() {
        return productSourceRepository.findAllByOrderByLibelleAsc();
    }
    
    // Convertir entité vers DTO
    public ProductSourceDto convertToDto(ProductSource productSource) {
        return new ProductSourceDto(
            productSource.getId(),
            productSource.getCode(),
            productSource.getLibelle(),
            productSource.getDescription()
        );
    }
    
    // Convertir DTO vers entité
    public ProductSource convertToEntity(ProductSourceDto dto) {
        ProductSource productSource = new ProductSource();
        productSource.setId(dto.getId());
        productSource.setCode(dto.getCode());
        productSource.setLibelle(dto.getLibelle());
        productSource.setDescription(dto.getDescription());
        return productSource;
    }
}
