package com.example.stockmanagementservice.dtos;

/**
 * DTO simple pour les produits marchands
 */
public class ProductMarchandDto {
    
    private Long id;
    private String code;
    private String libelle;
    private String description;
    
    // Constructeurs
    public ProductMarchandDto() {}
    
    public ProductMarchandDto(Long id, String code, String libelle, String description) {
        this.id = id;
        this.code = code;
        this.libelle = libelle;
        this.description = description;
    }
    
    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }
    
    public String getLibelle() { return libelle; }
    public void setLibelle(String libelle) { this.libelle = libelle; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
}
