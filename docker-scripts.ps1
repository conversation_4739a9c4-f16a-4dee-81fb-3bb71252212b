# PowerShell script for Docker operations

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "build", "logs", "clean", "status")]
    [string]$Action
)

switch ($Action) {
    "start" {
        Write-Host "🚀 Starting all microservices..." -ForegroundColor Green
        docker-compose up -d
        Write-Host "✅ All services started!" -ForegroundColor Green
        Write-Host "📊 Eureka Server: http://localhost:8761" -ForegroundColor Cyan
        Write-Host "🌐 API Gateway: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "📦 Stock Management: http://localhost:8081" -ForegroundColor Cyan
    }
    
    "stop" {
        Write-Host "🛑 Stopping all microservices..." -ForegroundColor Yellow
        docker-compose down
        Write-Host "✅ All services stopped!" -ForegroundColor Green
    }
    
    "restart" {
        Write-Host "🔄 Restarting all microservices..." -ForegroundColor Blue
        docker-compose down
        docker-compose up -d
        Write-Host "✅ All services restarted!" -ForegroundColor Green
    }
    
    "build" {
        Write-Host "🔨 Building all microservices..." -ForegroundColor Magenta
        docker-compose build --no-cache
        Write-Host "✅ All services built!" -ForegroundColor Green
    }
    
    "logs" {
        Write-Host "📋 Showing logs for all services..." -ForegroundColor Cyan
        docker-compose logs -f
    }
    
    "clean" {
        Write-Host "🧹 Cleaning up Docker resources..." -ForegroundColor Red
        docker-compose down -v --remove-orphans
        docker system prune -f
        Write-Host "✅ Cleanup completed!" -ForegroundColor Green
    }
    
    "status" {
        Write-Host "📊 Service Status:" -ForegroundColor Cyan
        docker-compose ps
    }
}

# Usage examples:
# .\docker-scripts.ps1 start    # Start all services
# .\docker-scripts.ps1 stop     # Stop all services
# .\docker-scripts.ps1 build    # Build all services
# .\docker-scripts.ps1 logs     # View logs
# .\docker-scripts.ps1 clean    # Clean up everything
