<component name="libraryTable">
  <library name="Maven: org.springframework.security:spring-security-crypto:6.5.1" type="java-imported" external-system-id="Maven">
    <properties groupId="org.springframework.security" artifactId="spring-security-crypto" version="6.5.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>