package com.example.stocmangementservice.model;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.*;

@Getter
@Setter
@Entity
@DiscriminatorValue("MARCHAND")
public class ProductMarchand extends Produit {

    public ProductMarchand() {
        super();
    }

    public ProductMarchand(long id) {
        super(id);
    }

    public ProductMarchand(long id, String libelle) {
        super(id, libelle);
    }

    public ProductMarchand(String libelle) {
        super(libelle);
    }

    public ProductMarchand(String libelle, String code) {
        super(libelle, code);
    }
} 