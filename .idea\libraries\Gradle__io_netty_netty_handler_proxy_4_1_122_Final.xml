<component name="libraryTable">
  <library name="Gradle: io.netty:netty-handler-proxy:4.1.122.Final" type="java-imported" external-system-id="GRADLE">
    <properties groupId="io.netty" artifactId="netty-handler-proxy" version="4.1.122.Final" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-handler-proxy/4.1.122.Final/17f96704a7fd492e6c1692a25e9d2bea78f06d20/netty-handler-proxy-4.1.122.Final.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.netty/netty-handler-proxy/4.1.122.Final/505836b36cfd3870cb14f785391f59e0a71d001f/netty-handler-proxy-4.1.122.Final-sources.jar!/" />
    </SOURCES>
  </library>
</component>