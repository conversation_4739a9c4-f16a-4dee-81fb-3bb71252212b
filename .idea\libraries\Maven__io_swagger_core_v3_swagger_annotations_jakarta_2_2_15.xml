<component name="libraryTable">
  <library name="Maven: io.swagger.core.v3:swagger-annotations-jakarta:2.2.15" type="java-imported" external-system-id="Maven">
    <properties groupId="io.swagger.core.v3" artifactId="swagger-annotations-jakarta" version="2.2.15" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15-sources.jar!/" />
    </SOURCES>
  </library>
</component>